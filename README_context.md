## QFlowAgent 写作上下文与节点流转指南（从零上手版）

本文面向“懂 LangGraph 基础（node/message）但不了解本项目”的读者，帮助你快速建立对写作流程与提示词拼装的整体认知：先讲“节点如何流转”，再讲“每个节点提供哪些信息、如何构建上下文”，最后再补充“信息优先级与预算机制”。

### 一、从 0 到 1：节点如何流转（高层流程）
- 设定阶段：
  - `bs_planner`（策划案/计划）→ `bs_steps`（执行步骤）→ `bs_refine`（精炼）。
  - 产出书名、简介、关键设定与执行步骤；将“粗设定”逐步精炼为“可用设定集”。
- 角色阶段 `writer_character`（多阶段）：
  - 先产出所有主要角色的“简介集合”，再逐个产出“详情”。
- 大纲阶段 `writer_outline`（多阶段）：
  - 先生成“整书分卷大纲”，再按“当前章节进度”细化必需的卷大纲。
- 场景阶段 `writer_scene`（多阶段）：
  - 先为每卷创建“空场景结构”，再按进度为具体卷产出“场景清单”。
- 写作阶段（仅一种模式）：
  - 流式写作→分章：`stream_write` 连续写入缓冲，达到阈值→`chapter_segment` 智能分章→回到 `stream_write` 循环。

流程跳转由各节点的前置检查决定；所有节点执行后会更新 `resume_info` 并持久化 checkpoint，便于恢复（应用层可通过 `config.configurable.checkpoint_id` 指定恢复点，支持 `latest`）。

#### 图示：节点流转（高层）

```mermaid
flowchart TD
  BP["bs_planner"] --> BS["bs_steps"]
  BS --> BR["bs_refine"]
  BR --> WC["writer_character"]
  WC --> WO["writer_outline"]
  WO --> WS["writer_scene"]
  WS --> SW["stream_write"]
  SW -->|"缓冲≥阈值"| SEG["chapter_segment"]
  SEG --> PNC["prepare_next_chapter"]
  PNC -->|"继续写作"| SW
  PNC -->|"补足场景/下一卷"| WS
  PNC --> END["__end__"]
```

### 二、统一的上下文构建规则（所有节点通用）

- 统一构建器 `ContextBuilder`（`src/context/context_builder.py`）：
  - `header()`：稳定系统前缀（工作语言、写作身份）；不注入动态时间以提升前缀缓存命中。
  - `section(title, content)`：以 `<title>...</title>` 包裹的结构化分节（进入 HumanMessage）。
  - `constraints(extra=None)`：统一的角色使用约束（进入 SystemMessage）。
  - `json_schema(schema)`：Schema 作为独立 SystemMessage；始终优先保留。
  - `demo()/previous_summary()/attach_retrieval()`：按需追加示例、历史摘要、检索片段。
  - `build(max_tokens=None, policy=None)`：输出 `[System, System(schema), Human]` 三段式消息。

- 标准分节名（`src/context/policies.py`）建议统一使用：`书籍信息`、`设定集`、`当前场景信息`、`相关角色设定`、`当前卷大纲（摘要）`、`卷级核心摘要（短）`、`最近缓冲区内容（避免重复衔接，仅参考，不要复述）`、`PREVIOUS_SUMMARY` 等。


#### 图示：上下文拼装分层

```mermaid
flowchart LR
  subgraph SYS["SystemMessage（稳定前缀，尽量不变）"]
    L["工作语言：中文 / 写作角色定位"]
    C["重要约束（角色使用约束等）"]
    S["JSON Schema（独立 SystemMessage）"]
  end
  subgraph HUM["HumanMessage（任务与上下文）"]
    H1["任务说明/执行模板"]
    H2["书籍信息"]
    H3["设定集"]
    H4["卷级核心摘要（短）"]
    H5["当前卷大纲（摘要）"]
    H6["当前场景信息"]
    H7["相关角色设定"]
    H8["PREVIOUS_SUMMARY（上一章摘要）"]
    H9["DEMO（示例）"]
    H10["最近缓冲区内容"]
  end
  SYS --> HUM
```

### 三、各节点的上下文与输出（按执行顺序）

#### 1) 设定规划 `bs_planner`
恢复由应用层通过 `config.configurable.thread_id/checkpoint_id` 控制，支持 `checkpoint_id=latest` 从最新 checkpoint 恢复。

#### 2) 设定规划 `bs_planner`
- 输入：`state.messages`（用户原始诉求）、可选 `book_name`/`demo_chapter`/`demo_bs`。
- 上下文：
  - `header()`
  - `section("创作计划任务", …)` + `section("计划格式", …)`
  - `json_schema(WriterPlanSchema)`
  - 生成后将 `state["messages"]` 附加到对话末尾（纳入用户原始对话）。
- 产出：`WriterPlan`（含步骤）；设置 `book_id` = `configurable.thread_id`，保存到 `setting/0-setting_plan.md`。
- 下一步：`bs_steps`。

#### 3) 设定步骤 `bs_steps`
- 输入：`writer_current_plan`、`refined_book_setting`（可能为空）。
- 上下文：
  - `header()`
  - `section("设定步骤执行", 基础模板 + 当前步骤执行模板)`（包含用户原始诉求、计划、步骤、未精炼内容）
  - 可选 `section("书名", …)`、`demo(...)`
- 产出：当前步骤执行结果（文本），保存 `setting/{i}-{step_title}.md`，并更新步骤状态。
- 下一步：继续 `bs_steps` 或视情况跳至 `bs_refine`（若需精炼）。

#### 4) 设定精炼 `bs_refine`
- 输入：`writer_current_plan`、需要精炼的内容（自动恢复）。
- 上下文：
  - `header()`
  - `section("设定精炼任务", …)`
  - 可选 `demo_chapter`/`demo_bs`
- 产出：`refined_book_setting`（设定集），保存 `setting/0.{id}-refined-booksetting.md`。
- 下一步：全部精炼完成→`writer_character`，否则回到 `bs_steps`。

#### 5) 角色设计 `writer_character`（多阶段）
- 阶段A：`init_character`
  - 上下文：`header()` + `section("角色设计任务", …)` + `json_schema(CharacterSummaryCollection)` + 可选 `demo`
  - 产出：`character_summaries`（角色简介集合），保存 `character/角色简介.md`
- 阶段B：`detailed_character`
  - 上下文：`header()` + `section("角色细化任务", …)` + `json_schema(CharacterDetail)`
  - 产出：`character_details`（角色详情集合），保存 `character/角色详情.md` 与单角色详情文件
- 完成后下一步：`writer_outline`。

#### 6) 大纲 `writer_outline`（多阶段）
- 阶段A：`init_volume_outlines`
  - 上下文：`header()` + `section("大纲任务", …)` + `json_schema(BookVolumeOutlinesSchema)` + 可选 `demo`
  - 产出：`book_detail.volume_outlines`，保存 `outline/整书分卷大纲.md`
- 阶段B：`refine_volume_outlines`
  - 上下文：`header()` + `section("大纲细化任务", …)` + `json_schema(VolumeOutline)` + 可选 `section("NOTE", …)`
  - 产出：指定卷的细化版本，保存 `outline/第{n}卷-细化大纲-v{version}.md`
- 完成后下一步：`writer_scene`。

#### 7) 场景设计 `writer_scene`（多阶段）
- 阶段A：`init_structure`（不调用 LLM）
  - 作用：为每卷创建空的 `VolumeSceneDesign` 结构，填入 `scene_design`。
- 阶段B：`design_volume`
  - 上下文：`header()` + `section("场景设计任务", …)` + `json_schema(VolumeSceneDesign)` + 可选 `demo`
  - 产出：第 n 卷的场景清单，保存 `scene/第{n}卷场景清单.md`
- 完成后下一步：`stream_write`。

#### 8) 流式写作 `stream_write`
- 输入：`book_detail`（含 `chapters` 与 `stream_buffer`）、`scene_design`、`character_details` 等。
- 上下文（顺序优化以提升前缀缓存命中）：
  1) `section("写作模式", STREAM_WRITING_GUIDANCE)`
  2) `section("写作要求", …)`
  3) `section("书籍信息", …)`
  4) `section("设定集", …)`
  5) `section("卷级核心摘要（短）", 主题/冲突/目标)`（若可得）
  6) `section("当前卷大纲（摘要）", …)`
  7) `section("当前场景信息", …)`（附带最近 3 个场景摘要）
  8) `section("未解悬念清单（短）", …)`（来自上一章 `think_todo_outline`）
  9) `section("相关角色设定", …)`
  10) `section("最近缓冲区内容（避免重复衔接，仅参考，不要复述）", …)`（高波动信息放末尾）
  11) `json_schema(Chapter)`
- 产出：把正文追加进 `book_detail.stream_buffer` 并保存 `chapters_stream/stream_buffer.md`。
- 下一步：缓冲达阈值→`chapter_segment`；否则留在 `stream_write` 继续。

##### 图示：流式写作 → 分章循环

```mermaid
flowchart TD
  SW["stream_write（追加缓冲）"] --> J{缓冲字数≥阈值?}
  J -- 否 --> SW
  J -- 是 --> SEG["chapter_segment（切章并轻润色）"]
  SEG --> K{剩余缓冲≥最小分章字数?}
  K -- 是 --> SEG
  K -- 否 --> SW
```

#### 9) 智能分章 `chapter_segment`
- 输入：`book_detail.stream_buffer`
- 上下文：`header()` + `section("智能分章任务", …)` + `json_schema(Chapter)`
- 产出：从缓冲中切出一章（3000–5000字），保存到 `chapter/第{n}章-标题.md`，并扣除缓冲。
- 下一步：缓冲仍然充足→继续 `chapter_segment`；否则回到 `stream_write`。

（已移除）章节写作 `chapter` 模式：统一采用“流式写作 + 智能分章”。

### 四、信息的优先级与组织（面向作者视角）

- 稳定前缀（System）优先：工作语言、作者角色、通用约束、Schema。尽量稳定且靠前，便于厂商前缀缓存。
- 长期稳定信息靠前：如“书籍信息”“设定集”“卷级核心摘要（短）”，有助于维持写作风格和长期一致性。
- 与当前写作强相关的信息中部：如“当前卷大纲（摘要）”“当前场景信息”“相关角色设定”。
- 高波动信息靠后：如“最近缓冲区内容”，以降低对前缀缓存的扰动。
- 历史脉络按需附带：如“上一章摘要（PREVIOUS_SUMMARY）”“未解悬念清单（短）”，帮助连贯但可在预算不足时优先裁剪。

### 五、技术细节：预算与截断策略（policy 驱动）

- `ContextBuilder.build(..., policy=...)` 在超预算时会按以下顺序裁剪：
  1) 先移除 `demo`
  2) 再移除 `PREVIOUS_SUMMARY`
  3) 再按 policy 的“可选分节标题”顺序移除
  4) 仍超预算则对 Human 正文做“渐进截断”（System 与 Schema 尽量保留）

- 关键 policy（可选分节）示例：
  - （章节模式已移除）
  - scene：`NOTE` → `书籍信息` → `相关角色设定` → `当前卷大纲（摘要）`
  - outline：`NOTE` → `书籍信息` → `相关角色设定` → `当前场景信息` → `当前卷大纲（摘要）`
  - stream：`NOTE` → `书籍信息` → `相关角色设定` → `卷级核心摘要（短）` → `当前卷大纲（摘要）` → `未解悬念清单（短）` → `最近缓冲区内容（避免重复衔接，仅参考，不要复述）`
  - bs_planner / bs_steps / character：按任务最小必要集精简

- Token 估算：`TokenTools.count_messages_approximately_tokens`

### 六、配置项速查（`RunnableConfig.configurable`）

- prompt_budget_tokens：上下文预算；触发策略化精简与截断。
- model_tier / llm_params：模型等级与参数（节点请求时透传）。
- book_name / demo_chapter / demo_bs：按需注入特定分节。
- 其他：`default_new_project`、`resume_book_id`、`enable_smart_character_management`、`segment_min_len`、`demo_chapter_cnt`。

### 七、扩展与最佳实践

- 新节点接入：
  - 使用 `ContextBuilder`：`header()` → 核心 `section(...)` → 按需 `constraints()/demo()/previous_summary()/json_schema(...)`。
  - 在 `build(max_tokens=..., policy=...)` 里选择匹配任务的 policy。
- 避免把历史消息塞进 System：历史对话通过 `State.messages` 聚合，或仅追加必要摘要。
- 对齐分节名：与内置策略一致可获得更好的预算裁剪效果。
- 检索片段注入：用 `attach_retrieval` 控制 `max_chars`，确保 Schema 与主任务清晰度。

### 参考文件

- 构建器：`src/context/context_builder.py::ContextBuilder`
- 策略与常量：`src/context/policies.py`
- 状态：`src/state.py::State`
- 运行入口：`src/functional/entrypoint.py`
- 节点框架：统一采用函数式节点 + `NodeKit`（见 `README_nodes.md`）
- 进度与恢复：`src/nodes/progress/`
