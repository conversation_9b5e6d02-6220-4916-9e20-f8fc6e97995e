import asyncio

from src.common.utils.time_utils import format_human_datetime
from src.langgraph.checkpoint import CheckpointManager


async def main():
    async with CheckpointManager.connect() as mgr:
        books = await mgr.list_books()
        if not books:
            print("No books found.")
            return
        for i, b in enumerate(books, start=1):
            print(
                f"{i}. book_id={b.book_id} name={b.book_name or '(未命名)'} created={format_human_datetime(b.created_ts)} updated={format_human_datetime(b.updated_ts)} representative_thread_id={b.representative_thread_id}"
            )
            cps = await mgr.list_checkpoints(b.book_id)
            if not cps:
                print("   checkpoints: []")
                continue
            print("   checkpoints:")
            for c in cps:
                print(
                    f"    - cp={c.checkpoint_id} ts={format_human_datetime(c.ts)} thread={c.thread_id} parent={c.parent_checkpoint_id}"
                )


if __name__ == "__main__":
    asyncio.run(main())
