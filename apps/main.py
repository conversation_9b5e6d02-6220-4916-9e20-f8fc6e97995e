#!/usr/bin/env python3
"""
🚀 QFlowAgent 统一启动器

这是QFlowAgent的主入口文件，集成了所有功能和配置选项。
用户可以通过修改下面的配置来控制应用行为。

使用方法：
    uv run apps/main.py

功能特性：
- 📋 统一配置管理，所有选项都有详细注释
- 🔄 支持checkpoint恢复和项目管理
- 🎯 支持指定book_id直接恢复
- 🎮 支持交互模式
- 📊 完整的事件流和进度跟踪
"""

import argparse
import asyncio
import logging

import uuid
from pathlib import Path
from typing import Any, Dict, Optional

import httpx

from cli_events import stream_graph_events

from langchain_core.messages import HumanMessage
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from src.init_log import init_log
from src.langgraph.checkpoint import CheckpointManager
from src.langgraph.entrypoint import build_qflow_workflow
from src.langgraph.memory.store_utils import DB_URI

from ui.terminal_selection import prompt_book_action

# =============================================================================
# 📋 基础配置区域
# =============================================================================

# 工作区路径
WORKSPACE_PATH = Path("./workspace")

# 人工审核功能已移除，统一自动模式

# 是否为新项目（True=创建新项目，False=恢复现有项目）
DEFAULT_NEW_PROJECT = True

# 数据库为空时的行为：现已固定为自动创建新项目（移除可配置项）

# =============================================================================
# 🔄 恢复配置区域
# =============================================================================

# 指定book_id直接恢复功能
# 取消注释下面的行并填入book_id来启用直接恢复功能
# 启用后将跳过交互式选择，直接恢复指定项目
RESUME_BOOK_ID: Optional[str] = None

# 指定thread_id恢复（可选，通常自动选择最新的）
# RESUME_THREAD_ID = "specific-thread-id"
RESUME_THREAD_ID: Optional[str] = None

# 指定checkpoint_id回退（可选，用于回退到历史状态）
# 支持特殊值："latest" 表示从最新的 checkpoint 恢复
# RESUME_CHECKPOINT_ID = "specific-checkpoint-id"
RESUME_CHECKPOINT_ID: Optional[str] = "latest"

# =============================================================================
# 🎯 运行模式配置
# =============================================================================

# =============================================================================
# 📊 显示和界面配置
# =============================================================================

# 最大预览长度（字符数）
MAX_PREVIEW_LEN = 400

# 流式预览长度（字符数）
STREAM_PREVIEW_LEN = 120

# =============================================================================
# 🤖 LLM和文本质量配置
# =============================================================================

# LLM生成文本的最小长度要求
LLM_TEXT_MIN_LENGTH = 200

# LLM文本敏感模式检测（检测占位符等问题文本）
LLM_TEXT_SENSITIVE_PATTERNS = [r"(?i)\b(placeholder|todo)\b"]

# LLM重试等级（"low"/"medium"/"high"）
LLM_TEXT_RETRY_TIER = "high"

# =============================================================================
# 📚 项目和流程配置
# =============================================================================

# 可选：通过 CLI 提供书名；不提供时由策划阶段自动确定
CLI_BOOK_NAME: Optional[str] = None
# API 配置（默认走 FastAPI 单步）
API_BASE = "http://127.0.0.1:2026"
USE_DIRECT = False


# 递归限制（防止无限循环）
RECURSION_LIMIT = 103

# 默认人类消息（用于新项目启动）
DEFAULT_HUMAN_MSG = """
请策划一部玄幻爽文小说, 包括宏大的世界观、修炼成长体系与法则、完善的战斗逻辑、有特色看点亮点等。
- 修炼体系不喜欢金丹筑基类的，倾向于偏魔法玄幻。
- 主角掌控极其罕见的空间与时间之力，其他角色大多是普通的魔法体系。
- 世界观宏大，史诗感强，有深度，伏笔设计精彩。
- 整体采用爽文发展，扮猪吃虎等各种爽点，不要出现主角被虐的情况
- 不要搞熵增熵减、熵值、系统管理员等这种大众看不懂的，或者脱离主流小说的奇怪设定
"""

# =============================================================================
# 🔧 高级配置（一般不需要修改）
# =============================================================================


# =============================================================================
# 🚀 主程序逻辑
# =============================================================================


class RunArgs(dict):
    pass


async def build_run_args() -> RunArgs:
    """
    统一产出一次运行计划：{mode: 'new'|'resume', thread_id, checkpoint_id?, book_id?, llm_input?}
    - 前置：清理“无策划案”的历史记录
    - 若设置 RESUME_BOOK_ID -> 直接恢复
    - 否则进入交互选择（含删除流程），直至返回 new/resume 的计划
    """
    async with CheckpointManager.connect() as mgr:
        # 若显式请求全局 latest：选择最近一个“下一节点不是 end”的 checkpoint 作为恢复点
        if (RESUME_CHECKPOINT_ID or "").strip().lower() == "latest":
            latest = await mgr.get_latest_checkpoint_global()
            if latest is not None:
                print(
                    f"\n🔄 全局最新恢复：book_id= {latest.book_id} thread_id= {latest.thread_id} checkpoint_id= {latest.checkpoint_id}"
                )
                use_cp = latest.checkpoint_id
                try:
                    # 若 latest 本身无 next 或 next==__end__，沿父链回退（兼容 old: next_node）
                    nxt = None
                    if isinstance(latest.metadata, dict):
                        nxt = (
                            latest.metadata.get("next")
                            if latest.metadata.get("next") is not None
                            else latest.metadata.get("next_node")
                        )
                    if not nxt or str(nxt).strip() in {"", "__end__"}:
                        cps = await mgr.list_checkpoints(latest.book_id)
                        id_map = {c.checkpoint_id: c for c in cps}
                        visited = set()
                        cid = latest.parent_checkpoint_id or latest.checkpoint_id
                        while cid and cid not in visited:
                            visited.add(cid)
                            s = id_map.get(cid)
                            if s is None:
                                break
                            nxt2 = None
                            if isinstance(s.metadata, dict):
                                nxt2 = (
                                    s.metadata.get("next")
                                    if s.metadata.get("next") is not None
                                    else s.metadata.get("next_node")
                                )
                            if nxt2 and str(nxt2).strip() not in {"", "__end__"}:
                                use_cp = s.checkpoint_id
                                break
                            cid = s.parent_checkpoint_id
                except Exception:
                    pass
                # 若仍未找到可继续的 checkpoint：深入读取各 checkpoint 的完整 state，基于 resume_info 判定
                try:
                    cps_all = await mgr.list_checkpoints(latest.book_id)
                    if cps_all:
                        for c in cps_all:
                            try:
                                cfg = {"configurable": {"thread_id": c.thread_id, "checkpoint_id": c.checkpoint_id}}
                                cp_data = await mgr.checkpointer.aget(cfg)  # type: ignore[attr-defined]
                                checkpoint = cp_data.checkpoint if hasattr(cp_data, "checkpoint") else cp_data
                                if isinstance(checkpoint, dict):
                                    values = checkpoint.get("values") or checkpoint.get("channel_values") or checkpoint
                                    resume_list = values.get("resume_info") if isinstance(values, dict) else None
                                    if isinstance(resume_list, list) and resume_list:
                                        last = resume_list[-1]
                                        if isinstance(last, dict):
                                            val = (
                                                last.get("next")
                                                if last.get("next") is not None
                                                else last.get("next_node")
                                            )
                                            if val and str(val).strip() not in {"", "__end__"}:
                                                return RunArgs(
                                                    mode="resume",
                                                    thread_id=c.thread_id,
                                                    checkpoint_id=c.checkpoint_id,
                                                    book_id=c.book_id,
                                                )
                            except Exception:
                                continue
                except Exception:
                    pass
                return RunArgs(
                    mode="resume",
                    thread_id=latest.thread_id,
                    checkpoint_id=use_cp,
                    book_id=latest.book_id,
                )
            # 无任何 checkpoint：后续走正常流程（可能新建或交互）
        # 直接恢复路径：为避免误删历史，先尝试恢复，再做清理
        if RESUME_BOOK_ID:
            print(f"\n🔄 直接恢复模式：book_id={RESUME_BOOK_ID}")
            thread_id = RESUME_THREAD_ID
            checkpoint_id = RESUME_CHECKPOINT_ID
            if not thread_id:
                try:
                    checkpoints = await mgr.list_checkpoints(RESUME_BOOK_ID or "")
                    if checkpoints:
                        latest = checkpoints[0]
                        thread_id = latest.thread_id or RESUME_BOOK_ID
                    else:
                        print(f"🆕 未找到历史记录，创建新项目：book_id={RESUME_BOOK_ID}")
                        llm_input = {"messages": [HumanMessage(content=DEFAULT_HUMAN_MSG)]}
                        return RunArgs(
                            mode="new", thread_id=RESUME_BOOK_ID, book_id=RESUME_BOOK_ID, llm_input=llm_input
                        )
                except Exception:
                    print(f"🆕 创建新项目：book_id={RESUME_BOOK_ID}")
                    thread_id = f"{uuid.uuid4()}"
                    return RunArgs(mode="new", thread_id=thread_id, book_id=RESUME_BOOK_ID)
            print(f"📖 恢复项目：book_id={RESUME_BOOK_ID}，thread_id={thread_id}")
            if checkpoint_id:
                print(f"⏪ 回退到checkpoint：{checkpoint_id}")
            return RunArgs(mode="resume", thread_id=thread_id, checkpoint_id=checkpoint_id, book_id=RESUME_BOOK_ID)

        # 启动时清理“无策划案”的历史记录（非直接恢复场景）
        try:
            deleted = await mgr.cleanup_checkpoints_without_plan(dry_run=False)
            if deleted > 0:
                print(f"🧹 已清理无策划案历史的线程数：{deleted}")
        except Exception:
            pass

        # 交互选择路径
        print("\n🎮 交互式选择模式")
        while True:
            action = await prompt_book_action(mgr)

            if action["action"] in {"new", "empty"}:
                thread_id = f"{uuid.uuid4()}"
                print(f"🆕 创建新项目：thread_id={thread_id}")
                llm_input = {"messages": [HumanMessage(content=DEFAULT_HUMAN_MSG)]}
                return RunArgs(mode="new", thread_id=thread_id, llm_input=llm_input)

            if action["action"] == "delete":
                try:
                    ans = input("是否先进行 dry-run 预览将被删除的资源清单？(y/N): ").strip().lower()
                except KeyboardInterrupt:
                    ans = "n"
                if ans == "y":
                    await mgr.delete_book(action["book_id"], workspace_path=WORKSPACE_PATH, dry_run=True)
                    try:
                        confirm = input("\n是否继续执行实际删除？(y/N): ").strip().lower()
                    except KeyboardInterrupt:
                        confirm = "n"
                    if confirm != "y":
                        print("已取消删除，返回主菜单。")
                        continue
                await mgr.delete_book(action["book_id"], workspace_path=WORKSPACE_PATH, dry_run=False)
                print(f"✅ 已删除 book_id={action['book_id']} 的全部数据")
                continue

            if action["action"] == "resume":
                if action.get("checkpoint_id"):
                    print(
                        f"\n⏪ 回退并恢复项目：book_id={action.get('book_id')}，thread_id={action.get('thread_id')}，"
                        f"checkpoint_id={action.get('checkpoint_id')}，书名={action.get('book_name') or '(未命名)'}\n"
                    )
                    return RunArgs(
                        mode="resume",
                        thread_id=action["thread_id"],
                        checkpoint_id=action.get("checkpoint_id"),
                        book_id=action.get("book_id"),
                    )
                else:
                    print(
                        f"\n🔄 恢复项目：book_id={action.get('book_id')}，thread_id={action.get('thread_id')}，"
                        f"书名={action.get('book_name') or '(未命名)'}\n"
                    )
                    return RunArgs(mode="resume", thread_id=action["thread_id"], book_id=action.get("book_id"))

            # 未知 action：回退为新建
            thread_id = f"{uuid.uuid4()}"
            print(f"🆕 创建新项目：thread_id={thread_id}")
            llm_input = {"messages": [HumanMessage(content=DEFAULT_HUMAN_MSG)]}
            return RunArgs(mode="new", thread_id=thread_id, llm_input=llm_input)


async def run_qflow(run_args: RunArgs) -> None:
    """
    单一入口：构建配置 -> 调用 qflow_graph
    - 处理 GraphRecursionError：若可获取 book_id，则允许回退并重试（从同一入口调用）。
    """
    while True:
        default_new_project = run_args.get("mode") == "new"
        checkpoint_id = run_args.get("checkpoint_id")

        # 若请求使用“最新”checkpoint（全局），动态解析为实际的 thread/book；若存在 parent，则使用其父 checkpoint 继续
        if isinstance(checkpoint_id, str) and checkpoint_id.strip().lower() == "latest":
            from src.langgraph.checkpoint import CheckpointManager as _CM

            async with _CM.connect() as mgr:
                latest = await mgr.get_latest_checkpoint_global()
                if latest is not None:
                    run_args["thread_id"] = latest.thread_id or run_args.get("thread_id")
                    run_args["book_id"] = latest.book_id or run_args.get("book_id")
                    checkpoint_id = latest.parent_checkpoint_id or latest.checkpoint_id
                else:
                    # 未找到全局最新，移除 checkpoint 指定
                    checkpoint_id = None

        # 若解析得到 checkpoint_id：尝试回退到可继续执行的父检查点（metadata.resume_info.next_node 可用）
        try:
            if checkpoint_id and run_args.get("book_id"):
                from src.langgraph.checkpoint import CheckpointManager as _CM

                async with _CM.connect() as mgr:
                    cps = await mgr.list_checkpoints(run_args["book_id"])
                    id_map = {c.checkpoint_id: c for c in cps}
                    visited = set()
                    cid = checkpoint_id
                    while cid and cid not in visited:
                        visited.add(cid)
                        s = id_map.get(cid)
                        if s is None:
                            break
                        meta = getattr(s, "metadata", {}) or {}
                        nxt = None
                        if isinstance(meta, dict):
                            nxt = meta.get("next") if meta.get("next") is not None else meta.get("next_node")
                        if nxt and str(nxt).strip() not in {"", "__end__"}:
                            # 命中可继续的检查点
                            run_args["thread_id"] = s.thread_id or run_args.get("thread_id")
                            checkpoint_id = s.checkpoint_id
                            break
                        cid = s.parent_checkpoint_id
                    else:
                        pass
        except Exception:
            pass

        # 先计算章节基线，再构建配置并注入
        base_chapter_cnt = 0
        try:
            if not default_new_project and run_args.get("book_id"):
                # 从最新 checkpoint 的 state 中提取章节数，避免在非 runnable 上下文触发 StoreManager 警告
                from src.langgraph.checkpoint import CheckpointManager as _CM

                async with _CM.connect() as _mgr:
                    latest_state = await _mgr.get_latest_state(run_args["book_id"])  # type: ignore[index]

                cnt = 0
                try:
                    bd = None
                    if isinstance(latest_state, dict) and "book_detail" in latest_state:
                        bd = latest_state.get("book_detail")
                    if bd is not None:
                        if hasattr(bd, "chapters"):
                            cnt = len(getattr(bd, "chapters") or [])
                        elif isinstance(bd, dict):
                            chs = bd.get("chapters") or []
                            cnt = len(chs) if isinstance(chs, list) else 0
                    if cnt == 0:
                        # 兜底深度扫描
                        def _scan(obj) -> int:
                            try:
                                if hasattr(obj, "chapters"):
                                    return len(getattr(obj, "chapters") or [])
                                if isinstance(obj, dict):
                                    if "chapters" in obj and isinstance(obj["chapters"], list):
                                        return len(obj["chapters"])
                                    for v in obj.values():
                                        n = _scan(v)
                                        if n:
                                            return n
                                if isinstance(obj, (list, tuple)):
                                    for v in obj:
                                        n = _scan(v)
                                        if n:
                                            return n
                            except Exception:
                                return 0
                            return 0

                        cnt = _scan(latest_state)
                except Exception:
                    cnt = 0
                base_chapter_cnt = int(cnt or 0)
        except Exception:
            base_chapter_cnt = 0

        config = build_config(
            thread_id=run_args["thread_id"],
            default_new_project=default_new_project,
            checkpoint_id=checkpoint_id,
        )
        # 默认走 FastAPI 单步 API；--direct 时直连内核
        if not USE_DIRECT:
            async with httpx.AsyncClient(timeout=120.0) as client:
                # 构建统一 payload 并调用 API
                payload = build_api_payload(run_args, default_new_project, checkpoint_id)
                r = await client.post(f"{API_BASE}/api/v1/flows/run", json=payload)
                r.raise_for_status()
                print(r.json())
            return

        try:
            if isinstance(config.get("configurable"), dict):
                config["configurable"]["chapter_cnt_base"] = int(base_chapter_cnt)
        except Exception:
            pass
        llm_input = run_args.get("llm_input") if default_new_project else None

        # Functional API 运行路径（默认）：使用 Functional Workflow + 事件流
        finput = {}
        if llm_input:
            finput.update(llm_input)
        # 为 Functional Entrypoint 构建 checkpointer（使用 async context 保障生命周期）
        async with AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer:
            await checkpointer.setup()
            qflow_workflow = build_qflow_workflow(checkpointer)
            # 直接以事件流运行，支持中断/审核
            await stream_graph_events(qflow_workflow, finput, config)
        return


def build_api_payload(
    run_args: Dict[str, Any], default_new_project: bool, checkpoint_id: Optional[str]
) -> Dict[str, Any]:
    """构建调用 FastAPI /flows/run 的 payload（统一入口）。"""
    payload: Dict[str, Any] = {
        "thread_id": run_args["thread_id"],
        "default_new_project": bool(default_new_project),
        "workspace_path": str(WORKSPACE_PATH),
    }
    if checkpoint_id:
        payload["checkpoint_id"] = checkpoint_id
    if CLI_BOOK_NAME:
        payload["book_name"] = CLI_BOOK_NAME
    # 新项目：注入默认人类消息
    if default_new_project:
        lli = run_args.get("llm_input")
        if lli:
            payload["llm_input"] = lli
        else:
            payload["llm_input"] = {"messages": [{"type": "human", "content": DEFAULT_HUMAN_MSG}]}
    return payload


def build_config(thread_id: str, default_new_project: bool, checkpoint_id: Optional[str] = None) -> dict:
    """构建配置字典"""
    # 重要：LangGraph 的 recursion_limit 需要位于 config 顶层，而不是 configurable 下
    config = {
        "recursion_limit": RECURSION_LIMIT,
        "configurable": {
            "thread_id": thread_id,
            "workspace_path": WORKSPACE_PATH,
            # 人工审核开关已移除
            "default_new_project": default_new_project,
            "max_preview_len": MAX_PREVIEW_LEN,
            "stream_preview_len": STREAM_PREVIEW_LEN,
            "llm_text_min_length": LLM_TEXT_MIN_LENGTH,
            "llm_text_sensitive_patterns": LLM_TEXT_SENSITIVE_PATTERNS,
            "llm_text_retry_tier": LLM_TEXT_RETRY_TIER,
        },
    }

    # 仅当通过 CLI 显式提供书名时注入；否则由 bs_planner 在计划阶段确定
    if CLI_BOOK_NAME:
        config["configurable"]["book_name"] = CLI_BOOK_NAME

    # 添加checkpoint_id（如果指定）
    if checkpoint_id:
        config["configurable"]["checkpoint_id"] = checkpoint_id

    return config


# =========================== CLI 参数解析 ===========================


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="QFlowAgent 统一启动器",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )
    # 基础
    parser.add_argument("--workspace", dest="workspace", type=Path, default=WORKSPACE_PATH, help="工作区路径")
    # 人工审核 CLI 参数已移除
    # 恢复/回退
    parser.add_argument("--resume-book-id", dest="resume_book_id", help="指定 book_id 直接恢复；提供则跳过交互")
    parser.add_argument("--resume-thread-id", dest="resume_thread_id", help="指定 thread_id 恢复（可选）")
    parser.add_argument(
        "--checkpoint-id",
        dest="checkpoint_id",
        help="指定 checkpoint_id 回退（可选，支持 special: 'latest' 表示使用最新 checkpoint）",
    )

    # 空库时行为：配置项已移除，固定为自动创建

    # 运行模式
    parser.add_argument("--book-name", dest="book_name", default=None, help="新项目书名（可选，不传则由策划案确定）")

    # API 参数（默认走 FastAPI 单步）
    parser.add_argument("--api-base", dest="api_base", default=API_BASE, help="FastAPI 基础地址")
    parser.add_argument("--direct", dest="use_direct", action="store_true", help="直连内核（开发期过渡开关）")

    # 显示
    parser.add_argument("--max-preview", dest="max_preview_len", type=int, default=MAX_PREVIEW_LEN, help="最大预览长度")
    parser.add_argument(
        "--stream-preview", dest="stream_preview_len", type=int, default=STREAM_PREVIEW_LEN, help="流式预览长度"
    )

    # LLM/文本质量
    parser.add_argument(
        "--min-text-len", dest="llm_text_min_length", type=int, default=LLM_TEXT_MIN_LENGTH, help="LLM文本最小长度"
    )
    parser.add_argument(
        "--sensitive-pattern",
        dest="llm_text_sensitive_patterns",
        action="append",
        help="敏感文本正则，可多次；不传则沿用默认",
    )
    parser.add_argument(
        "--retry-tier",
        dest="llm_text_retry_tier",
        choices=["low", "medium", "high"],
        default=LLM_TEXT_RETRY_TIER,
        help="LLM 重试等级",
    )

    # 其他
    parser.add_argument("--recursion-limit", dest="recursion_limit", type=int, default=RECURSION_LIMIT, help="递归限制")

    return parser.parse_args()


def apply_args(args: argparse.Namespace) -> None:
    global WORKSPACE_PATH
    global RESUME_BOOK_ID, RESUME_THREAD_ID, RESUME_CHECKPOINT_ID
    global CLI_BOOK_NAME
    global MAX_PREVIEW_LEN, STREAM_PREVIEW_LEN
    global LLM_TEXT_MIN_LENGTH, LLM_TEXT_SENSITIVE_PATTERNS, LLM_TEXT_RETRY_TIER
    global RECURSION_LIMIT
    global API_BASE, USE_DIRECT

    WORKSPACE_PATH = args.workspace or WORKSPACE_PATH

    RESUME_BOOK_ID = args.resume_book_id or RESUME_BOOK_ID
    RESUME_THREAD_ID = args.resume_thread_id or RESUME_THREAD_ID
    RESUME_CHECKPOINT_ID = args.checkpoint_id or RESUME_CHECKPOINT_ID

    # 空库行为已固定为自动创建，不再读取参数

    CLI_BOOK_NAME = args.book_name or CLI_BOOK_NAME

    API_BASE = args.api_base or API_BASE
    USE_DIRECT = bool(args.use_direct) or USE_DIRECT

    MAX_PREVIEW_LEN = args.max_preview_len or MAX_PREVIEW_LEN
    STREAM_PREVIEW_LEN = args.stream_preview_len or STREAM_PREVIEW_LEN

    LLM_TEXT_MIN_LENGTH = args.llm_text_min_length or LLM_TEXT_MIN_LENGTH
    LLM_TEXT_SENSITIVE_PATTERNS = args.llm_text_sensitive_patterns or LLM_TEXT_SENSITIVE_PATTERNS
    LLM_TEXT_RETRY_TIER = args.llm_text_retry_tier or LLM_TEXT_RETRY_TIER

    RECURSION_LIMIT = args.recursion_limit or RECURSION_LIMIT


async def main() -> None:
    """主入口函数"""
    init_log()
    logger = logging.getLogger(__name__)

    # 参数优先
    args = parse_args()
    apply_args(args)

    print("=" * 80)
    print("🚀 QFlowAgent 统一启动器")
    print("=" * 80)

    # 显示当前配置（参数生效后）
    print("📋 当前配置：")
    print(f"   工作区路径: {WORKSPACE_PATH}")
    if CLI_BOOK_NAME:
        print(f"   指定书名: {CLI_BOOK_NAME}")
    if RESUME_BOOK_ID:
        print(f"   直接恢复: {RESUME_BOOK_ID}")
    print()

    try:
        run_args = await build_run_args()
        await run_qflow(run_args)

    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
        print(f"\n❌ 程序执行出错: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
    print("main end")
