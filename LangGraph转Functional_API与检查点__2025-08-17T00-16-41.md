[/] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Investigate current src/langgraph for Graph API remnants and LLM call sites DESCRIPTION:Identify all files/functions still returning Command/goto/update (Graph API style), map current control flow, and list all LLM call sites that should be @task. Output: inventory of targets to refactor and a proposed control flow owned by entrypoint.
-[x] NAME:Refactor entrypoint and nodes to Functional API (phase 1) – control flow extraction and return types DESCRIPTION:1) Change node fns to return plain dict updates (no Command/goto), isolate decision logic into entrypoint. 2) Rewrite entrypoint to own workflow loop based on state and simple signals. 3) Normalize @task usage only on LLM-calling fns. 4) Adjust State.serialize/deserialize unchanged. 5) Keep same public exports.
-[x] NAME:Refactor individual nodes (phase 2) – writer_outline, writer_scene, stream_write, chapter_segment, prepare_next_chapter, bs_* DESCRIPTION:For each node: remove Command/goto, split pure computation vs routing, ensure @task wraps only LLM I/O. Adapt call sites and return shapes. Update concurrent @task helpers remain tasks. Ensure no goto/add used.
-[x] NAME:Wire-up new control flow in entrypoint – explicit step function DESCRIPTION:Implement a deterministic step() inside entrypoint that decides next_node from state and the last node outputs. Ensure checkpoint saving semantics: only saved on @task returns, and entrypoint.final saves entire state. Provide stream writer annotations.
-[x] NAME:Validation: run unit tests / minimal e2e with dummy checkpointer DESCRIPTION:Add/adjust tests to simulate workflow with stub LLM returning deterministic values. Validate that checkpoints are saved after each @task and recovery works. Provide at least 10 scenario inputs/expected transitions.
-[/] NAME:Refactor entrypoint: inline node calls in step() (remove _run_one) DESCRIPTION:Remove _run_one layer. In qflow_workflow_impl, step() should directly await node functions per chosen next_node. Ensure @task checkpoints still occur by wrapping node calls with @task or migrating task boundaries appropriately.
-[ ] NAME:Add mock tier to NodeKit llm_json/llm_text DESCRIPTION:Support config.configurable.model_tier == 'mock' or 'test' to return deterministic fake structures/text for all LLM calls, without external network. Keep behavior realistic for schemas.
-[ ] NAME:Add 30+ tests covering step decisions and recovery DESCRIPTION:Unit and lightweight integration tests: planner/steps/refine/character/outline/scene/stream/segment/prepare and recovery from checkpoints. Use mock tier.
-[ ] NAME:Update README_nodes.md and related docs to Functional API DESCRIPTION:Remove Command/Send examples or mark as legacy; add Functional API examples for concurrent tasks using @task and aggregation.