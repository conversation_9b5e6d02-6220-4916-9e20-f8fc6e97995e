# 变量与上下文设计：State / RuntimeContext / Configurable

本文面向本项目开发者，系统说明三类“变量域”的职责边界、生命周期与使用方式，并给出放置规则与典型示例，确保后续开发保持一致性与可维护性。

---

## 总览与对照

- graph_state（State）
  - 含义：流程“业务状态”。在图的执行过程中不断被节点更新，并由 LangGraph Checkpointer 持久化，可断点恢复/回放。
  - 生命周期：跨步骤，跨进程，跨会话；可被恢复。
  - 可变性：可变（每个节点写入/合并）。
  - 典型内容：计划/设定/大纲/场景/章节、resume_info、skip_counters、流式/入库 join 的观察字段等。

- runtime_context（RuntimeContext）
  - 含义：一次运行期间的“非持久化依赖/句柄/上下文”。适用于无法/不应该序列化、仅对本次 run 有意义的对象。
  - 生命周期：单次运行（invoke/stream），运行结束后即失效；不进入 checkpoint。
  - 可变性：建议只读（静态运行期上下文）。
  - 典型内容：外部服务/DB 连接、工具注册表、日志/指标收集器、一次性缓存、已绑定工具的模型实例等。

- configurable（RunnableConfig.configurable）
  - 含义：用户/运维可配置的“运行参数”。用于复现、回放与可观察，通常从 CLI/ENV 注入。
  - 生命周期：随本次图执行有效；不自动持久化（但可被外部记录用于复现实验）。
  - 可变性：调用前设定，节点内部可读取使用（不建议在节点内改写）。
  - 典型内容：工作区路径、显示/流式预览策略、审核开关、递归限制、模型等级与参数、提示词预算、用户输入的书名等。

> 三者组合：State 负责“可恢复的业务结果/进度”，RuntimeContext 负责“执行期的依赖/句柄”，Configurable 负责“可复现的运行配置”。

---

## 放置规则（决策树）

- 需要 checkpoint 恢复/回放？
  - 是 → 放 State（graph_state）。
  - 否 → 继续：
    - 是否不可/不宜序列化，或仅本次运行有效的依赖/句柄？
      - 是 → 放 RuntimeContext。
      - 否 → 放 Configurable（例如“显示策略/阈值/模型等级”等用户可配置项）。

- 来自用户/CLI/ENV、需要复现实验或便于观测？
  - 放 Configurable。

- 会在节点间演进（执行结果、进度、队列观测）且需恢复？
  - 放 State。

- 属于工具/外部系统的 client、缓存、logger、metrics 等不可持久对象？
  - 放 RuntimeContext。

---

## 业务字段清单与含义

以下列出“当前项目实际使用”的三类变量域的主要业务字段与含义，开发时请严格按此放置与解读。

### A. State（graph_state）

- messages：对话消息聚合（用于 LLM 历史上下文）。
- user_original_request：初始的人类请求文本（便于各节点引用）。
- writer_current_plan：WriterPlan，策划案对象（含步骤、进度）。
- refined_book_setting / refined_book_outlines：精炼后的设定/大纲文本。
- book_id / book_name：书籍唯一标识与名称（book_id 通常等于首次 thread_id）。
- book_detail：BookDetail（含分卷大纲、章节、流式缓冲等综合结构）。
- character_summaries / character_details：角色摘要/详情集合。
- scene_design：BookSceneDesign（按卷的场景清单与要素）。
  
- （已移除）skip_counters：历史审核跳过计数。
- resume_info：步骤级“可读摘要”列表，用于 checkpoint 列表与 CLI 收尾显示（节点成功后追加）。
- 流式写作 join 观测/限流（由 stream_write → post-join）：
  - stream_post_batch_id / stream_post_chunks / stream_post_expected_indices
  - stream_post_pending_indices / stream_post_dispatched_indices
  - stream_post_dispatch_times / stream_post_requeue_counts
  - stream_post_inflight_count / stream_post_inflight_preview
- 章节入库 join 观测/限流（由 chapter_segment → store-join）：
  - chapter_store_expected_numbers

以上全部需要 checkpoint 可恢复，因此放在 State。

### B. Configurable（config.configurable）

- 基础与身份
  - thread_id：本次运行的线程标识（在本项目中即 book_id）。
  - book_name：书名（可选；未提供时由策划阶段产出）。
  - checkpoint_id：显式指定回退点进行恢复（可选）。
  - workspace_path：工作区根目录（文件落盘前缀）。

- 显示与 CLI 交互
  - max_preview_len：事件预览最大长度。
  - stream_preview_len：模型 token 流节流长度。
  

- （已移除）审核相关配置：enable_interrupt_review、skip_policies。
  - recursion_limit：LangGraph 最大递归步数（注意：此键位于 config 顶层，而非 configurable 内）。

- 模型请求与预算
  - model_tier：LOW|MEDIUM|HIGH（可用于节点侧模型选择与自动重试策略）。
  - llm_params：模型调用级参数覆盖（max_tokens、temperature 等）
  - prompt_budget_tokens：ContextBuilder 的 budget，用于策略化裁剪与截断。

- 文本稳健性（NodeKit.llm_text）
  - llm_text_min_length：文本最小长度阈值；过短触发回退重试。
  - llm_text_sensitive_patterns：敏感模式正则（字符串或数组）；命中触发回退重试。
  - llm_text_retry_tier：回退重试时升级的模型等级（low|medium|high）。

- 写作流程参数
  - segment_min_len / segment_max_len：分章的最小/最大长度阈值（chapter_segment 策略）。
  - stream_post_chunk_size：流式后处理的分块大小（默认 2000）。
  - stream_write_max_self_loops：流写自循环上限，达到后强制进入分章（默认 8）。
  - chapter_cnt_base：应用层自动注入的章节基线（只读）。

> 以上为“可复现、可观测”的运行参数，均不进入 State；由 CLI/入口构造注入。

### C. RuntimeContext（runtime_context）

- 当前不定义固定字段，承载“一次运行有效、不可序列化/不应持久化”的依赖/句柄，例如：
  - 外部服务 client（数据库/向量检索/嵌入/MCP 客户端等）
  - 工具注册表、日志/指标收集器
  - 一次性缓存/路由器/已绑定工具的模型实例

RuntimeContext 仅用于“执行期依赖”，不放用户配置；也不承载需要恢复的业务数据。

---

## 业务放置原则（复述）

- 可恢复的业务产物/进度 → State
- 可复现/可外显的运行参数 → Configurable
- 仅本次运行期的依赖/句柄 → RuntimeContext

在有疑问时，优先根据“是否需要 checkpoint 恢复”“是否需要外显/复现实验”判断放置位置。

---

## 典型键清单（当前项目）

- State（节选）
  - `messages`、`writer_current_plan`、`refined_book_setting`、`book_detail`
  - `character_summaries`、`character_details`、`scene_design`
  - `resume_info`
  - `skip_counters`
  - 流式后处理 join：`stream_post_*`；章节入库已简化为单节点，仅保留 `chapter_store_expected_numbers` 与结果键 `chapter_store_result_<n>`

- Configurable（节选）
  - 运行与展示：`workspace_path`、`max_preview_len`、`stream_preview_len`
  - 执行策略：`recursion_limit`
  - 模型与预算：`model_tier`、`llm_params`、`prompt_budget_tokens`
  - 业务输入：`book_name`、`checkpoint_id`、`thread_id`

- RuntimeContext（建议承载）
  - 外部服务 client：向量检索/嵌入/数据库/MCP 客户端
  - 工具注册表、日志/指标收集器
  - 一次性缓存/路由器/已绑定工具的模型实例

> 注意：`workspace_path` 已回归 Config（而非 Runtime），因为它属于用户/运维配置，需可复现与外显，不应被 checkpoint。

---

## Do / Don’t（最佳实践）

- Do：
  - 将“需要恢复的过程数据/进度”放 State（例如 resume_info、队列观测、业务产物）。
  - 将“可复现/可外显的运行参数”放 Config（例如目录、显示、阈值、模型等级/参数）。
  - 将“依赖/句柄/不可序列化对象”放 RuntimeContext（例如 DB 客户端、logger、tools 注册表）。
  - 通过 `context_schema=RuntimeContext` 在主图挂载运行期上下文；节点内用 `get_runtime()` 获取。

- Don’t：
  - 不要把临时依赖放进 State（会污染 checkpoint 并导致恢复问题）。
  - 不要把可复现的配置放进 RuntimeContext（会降低可观测性/复现性）。
  - 不要在节点里随意改写 Config；Config 应由入口统一构造。

---

## 迁移指引（从“混用”到“三分治”）

1) 识别：扫描节点/工具，标记当前通过参数/全局/State 传递的值的“复现性/持久化/依赖性”。
2) 决策：按上文决策树归类到 State / RuntimeContext / Configurable。
3) 重构：
   - 运行参数 → Config：入口统一构造 `config["configurable"]`；节点用 `config` 读取。
   - 依赖/句柄 → Runtime：在编译图时构造 RuntimeContext；节点 `get_runtime()` 读取。
   - 业务状态 → State：节点返回 `Command(update=...)` 或字典合并写入。
4) 校验：
   - 断点恢复能否完整复现业务状态？
   - 替换模型/参数/目录等 Config 后，行为可复现且易观测？
   - 运行结束后是否无 Runtime 残留副作用？

---

## 参考位置

- 入口：`src/functional/entrypoint.py`（Functional API：qflow_workflow）
- 运行期上下文类型：`src/runtime/runtime_context.py`
- 状态定义与序列化：`src/state.py`
- CLI 入口与 Config 构造：`apps/main.py::build_config()`
- 节点工具层：`src/nodes/common/node_kit.py`
- 文件系统工具（从 Config 读取 workspace）：`src/common/utils/filesystem_utils.py`

---

以上约定是本项目新增/改造功能的必遵循规则。开发时若有边界拿不准，优先依据“是否需要 checkpoint 恢复”“是否需要可复现实验/外显配置”“是否为仅执行期有效的依赖/句柄”三问来决策放置域。
