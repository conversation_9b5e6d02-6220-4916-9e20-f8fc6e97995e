from __future__ import annotations

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class BookSummary(BaseModel):
    book_id: str
    book_name: Optional[str] = None
    created_ts: Optional[str] = None
    updated_ts: Optional[str] = None
    representative_thread_id: str
    thread_ids: List[str]
    meta: Dict[str, Any] = Field(default_factory=dict)


class CheckpointItem(BaseModel):
    checkpoint_id: str
    thread_id: str
    ts: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    parent_checkpoint_id: Optional[str] = None


class CheckpointNode(BaseModel):
    id: str
    parent: Optional[str] = None
    ts: Optional[str] = None
    thread_id: str
    metadata: Dict[str, Any] = Field(default_factory=dict)


class CheckpointTreeResponse(BaseModel):
    roots: List[str]
    nodes: Dict[str, CheckpointNode]
    children: Dict[str, List[str]]


class ReaderChapter(BaseModel):
    number: int
    title: str
    content: str


class ReaderProgress(BaseModel):
    steps: int = 0
    buffer_len: int = 0
    chapter_count: int = 0
    writing_state: Optional[str] = None


class ReaderProjection(BaseModel):
    chapters: List[ReaderChapter] = Field(default_factory=list)
    stream_buffer: str = ""
    progress: ReaderProgress = Field(default_factory=ReaderProgress)


class ReaderStateResponse(BaseModel):
    raw_state: Dict[str, Any] = Field(default_factory=dict)
    reader_projection: ReaderProjection
    meta: Dict[str, Any] = Field(default_factory=dict)


class NewBookInput(BaseModel):
    book_name: Optional[str] = None
    prompt: Optional[str] = None
    llm_input: Optional[Dict[str, Any]] = None
    default_new_project: Optional[bool] = True
    recursion_limit: Optional[int] = Field(default=1, ge=1)


class NewBookOutput(BaseModel):
    book_id: str
    thread_id: str
    next: Optional[str]
    steps: int


class ContinueInput(BaseModel):
    checkpoint_id: Optional[str] = None
    prompt: Optional[str] = None
    llm_input: Optional[Dict[str, Any]] = None
    recursion_limit: Optional[int] = Field(default=1, ge=1, description="本次续写的最大递归步数")


# ============ Full State (Everything) ============
class BookFullProjection(BaseModel):
    plan: Optional[Dict[str, Any]] = None
    setting: Dict[str, Any] = Field(default_factory=dict)
    outlines: Dict[str, Any] = Field(default_factory=dict)
    characters: Dict[str, Any] = Field(default_factory=dict)
    scenes: Dict[str, Any] = Field(default_factory=dict)
    progress: ReaderProgress = Field(default_factory=ReaderProgress)


class BookFullStateResponse(BaseModel):
    raw_state: Dict[str, Any] = Field(default_factory=dict)
    projection: BookFullProjection
    meta: Dict[str, Any] = Field(default_factory=dict)


__all__ = [
    "BookSummary",
    "CheckpointItem",
    "CheckpointTreeResponse",
    "CheckpointNode",
    "ReaderChapter",
    "ReaderProgress",
    "ReaderProjection",
    "ReaderStateResponse",
    "NewBookInput",
    "NewBookOutput",
    "ContinueInput",
    "BookFullProjection",
    "BookFullStateResponse",
]
