from __future__ import annotations

import json

from typing import Optional

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException, Query, Request
from fastapi.responses import StreamingResponse

from ..schemas.books import (
    BookSummary,
    CheckpointItem,
    CheckpointTreeResponse,
    NewBookInput,
    NewBookOutput,
    ReaderStateResponse,
)
from ..services.books_service import BooksService

router = APIRouter(prefix="/api/v1/books", tags=["books"])


def get_service() -> BooksService:
    return BooksService()


@router.get("", response_model=list[BookSummary])
async def list_books(request: Request, service: BooksService = Depends(get_service)):
    try:
        return await service.list_books()
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/checkpoints")
async def list_checkpoints(
    request: Request,
    book_id: str,
    flat: bool = Query(default=False),
    service: BooksService = Depends(get_service),
):
    try:
        data = await service.list_checkpoints(book_id, flat=flat)
        if flat:
            return [CheckpointItem(**item) for item in data]
        return CheckpointTreeResponse(**data)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/state/latest", response_model=ReaderStateResponse)
async def get_latest_state(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(default=None),
    service: BooksService = Depends(get_service),
):
    try:
        data = await service.get_reader_state(book_id, checkpoint_id=checkpoint_id)
        return ReaderStateResponse(**data)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.post("", response_model=NewBookOutput)
async def create_book(request: Request, body: NewBookInput, service: BooksService = Depends(get_service)):
    try:
        data = await service.create_book(body.model_dump(exclude_none=True))
        return NewBookOutput(**data)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/continue")
async def continue_book(
    request: Request,
    book_id: str,
    payload: Optional[str] = Query(default=None),
    service: BooksService = Depends(get_service),
):
    async def event_gen():
        rid = getattr(request.state, "request_id", None)
        try:
            body_dict = {}
            if payload:
                try:
                    body_dict = json.loads(payload)
                except Exception:
                    body_dict = {}
            # values: 每个节点触发一次，并行节点合并触发一次；将state返回上来
            # message: human的没有，llm请求是流式的token by token;
            # updates: 返回每个节点的upate内容
            stream_mode = ["values", "message", "updates", "custom"]
            async for ev in service.continue_book(book_id, body_dict, stream_mode=stream_mode):
                if isinstance(ev, tuple) and len(ev) == 2:
                    etype, evalue = ev
                else:
                    etype, evalue = "updates", ev
                event_obj = {"type": etype, "value": evalue, "request_id": rid}
                yield f"data: {json.dumps(event_obj, ensure_ascii=False)}\n\n"
            yield f"data: {json.dumps({'type': 'success', 'value': None, 'request_id': rid}, ensure_ascii=False)}\n\n"
        except Exception as e:
            err_obj = {"type": "error", "value": str(e), "request_id": rid}
            yield f"data: {json.dumps(err_obj, ensure_ascii=False)}\n\n"
        finally:
            yield f"data: {json.dumps({'type': 'close', 'value': None, 'request_id': rid}, ensure_ascii=False)}\n\n"

    headers = {"Cache-Control": "no-cache", "Connection": "keep-alive"}
    return StreamingResponse(event_gen(), media_type="text/event-stream", headers=headers)


@router.get("/{book_id}/state/full")
async def get_full_state(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(default=None),
    service: BooksService = Depends(get_service),
):
    try:
        return await service.get_full_state(book_id, checkpoint_id=checkpoint_id)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/plan")
async def get_plan(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(None),
    service: BooksService = Depends(get_service),
):
    try:
        return await service.get_plan(book_id, checkpoint_id=checkpoint_id)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/setting")
async def get_setting(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(None),
    service: BooksService = Depends(get_service),
):
    try:
        return await service.get_setting(book_id, checkpoint_id=checkpoint_id)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/outlines")
async def get_outlines(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(None),
    service: BooksService = Depends(get_service),
):
    try:
        return await service.get_outlines(book_id, checkpoint_id=checkpoint_id)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/characters")
async def get_characters(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(None),
    service: BooksService = Depends(get_service),
):
    try:
        return await service.get_characters(book_id, checkpoint_id=checkpoint_id)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})


@router.get("/{book_id}/scenes")
async def get_scenes(
    request: Request,
    book_id: str,
    checkpoint_id: Optional[str] = Query(None),
    service: BooksService = Depends(get_service),
):
    try:
        return await service.get_scenes(book_id, checkpoint_id=checkpoint_id)
    except Exception as e:
        rid = getattr(request.state, "request_id", None)
        raise HTTPException(status_code=500, detail={"error": str(e), "request_id": rid})
