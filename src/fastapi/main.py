from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.init_log import init_log

from .routers import health


@asynccontextmanager
async def lifespan(app: FastAPI):
    # startup
    init_log()
    yield
    # shutdown (noop)


app = FastAPI(title="QFlowAgent API", version="0.1.0", lifespan=lifespan)

# CORS for local dev (Vite default ports)
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:5174",
        "http://127.0.0.1:5174",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# include routers
app.include_router(health.router)
from .routers import books

app.include_router(books.router)
