from __future__ import annotations

import uuid

from typing import Any, Dict, List, Optional

from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver

from src.langgraph.checkpoint.checkpoint_manager import CheckpointManager
from src.langgraph.memory.store_utils import DB_URI


def _ensure_state_dict(checkpoint: Dict[str, Any]) -> Dict[str, Any]:
    """抽取最近一次的 state dict。兼容不同 checkpoint 格式。"""
    if not isinstance(checkpoint, dict):
        return {}
    # 兼容旧字段
    for key in ("values", "state"):
        v = checkpoint.get(key)
        if isinstance(v, dict):
            return v
    # 优先从 channel_values.__previous__ 提取
    channel_values = checkpoint.get("channel_values") or {}
    if isinstance(channel_values, dict):
        prev = channel_values.get("__previous__")
        if isinstance(prev, dict):
            return prev
    # 最后尝试本体
    return checkpoint if isinstance(checkpoint, dict) else {}


async def _resolve_thread_and_cp(
    mgr: CheckpointManager,
    book_id: str,
    checkpoint_id: Optional[str],
    thread_id: Optional[str] = None,
):
    """优先使用前端传入的 thread_id + checkpoint_id 直接定位 state。
    - 若仅提供 thread_id：在该书的该线程中选择最新 checkpoint
    - 若仅提供 checkpoint_id：回退到原有逻辑，解析所在线程
    - 若均未提供：使用该书全局最新 checkpoint
    返回: (thread_id, checkpoint_id, ts)
    """
    target_thread_id: Optional[str] = thread_id or None
    target_checkpoint_id: Optional[str] = checkpoint_id
    ts: Optional[str] = None

    cps = await mgr.list_checkpoints(book_id)
    if not cps:
        return None, None, None

    # 情况A：提供了 thread_id 但没提供 checkpoint_id → 取该线程最新一条
    if target_thread_id and not target_checkpoint_id:
        for c in cps:
            if c.thread_id == target_thread_id:
                target_checkpoint_id = c.checkpoint_id
                ts = c.ts
                break

    # 情况B：仅提供 checkpoint_id（或A未命中）→ 找到对应线程
    if target_checkpoint_id and not target_thread_id:
        for c in cps:
            if c.checkpoint_id == target_checkpoint_id:
                target_thread_id = c.thread_id
                ts = c.ts
                break

    # 情况C：均未提供 → 使用全局最新一条
    if not target_thread_id and not target_checkpoint_id:
        target_thread_id = cps[0].thread_id
        target_checkpoint_id = cps[0].checkpoint_id
        ts = cps[0].ts

    return target_thread_id, target_checkpoint_id, ts


async def _load_state(mgr: CheckpointManager, thread_id: Optional[str], checkpoint_id: Optional[str]) -> Dict[str, Any]:
    if not thread_id or not checkpoint_id:
        return {}
    cfg = {"configurable": {"thread_id": thread_id, "checkpoint_id": checkpoint_id}}
    cp_data = await mgr.checkpointer.aget(cfg)  # type: ignore[attr-defined]
    checkpoint = cp_data.checkpoint if hasattr(cp_data, "checkpoint") else cp_data
    return _ensure_state_dict(checkpoint)


class BooksService:
    async def list_books(self) -> List[Dict[str, Any]]:
        async with CheckpointManager.connect() as mgr:
            groups = await mgr.list_books()
            return [
                {
                    "book_id": g.book_id,
                    "book_name": g.book_name,
                    "created_ts": g.created_ts,
                    "updated_ts": g.updated_ts,
                    "representative_thread_id": g.representative_thread_id,
                    "thread_ids": g.thread_ids,
                    "meta": g.meta,
                }
                for g in groups
            ]

    async def list_checkpoints(self, book_id: str, *, flat: bool = False) -> Any:
        async with CheckpointManager.connect() as mgr:
            if flat:
                return await mgr.list_book_checkpoint_pairs(book_id)
            roots, nodes, children = await mgr.build_checkpoint_tree(book_id)
            # 将 nodes 值标准化
            norm_nodes = {
                k: {
                    "id": v.get("id"),
                    "parent": v.get("parent"),
                    "ts": v.get("ts"),
                    "thread_id": v.get("thread_id"),
                    "metadata": v.get("metadata") or {},
                }
                for k, v in nodes.items()
            }
            return {"roots": roots, "nodes": norm_nodes, "children": children}

    async def get_reader_state(
        self, book_id: str, *, checkpoint_id: Optional[str], thread_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """返回 raw_state 与 reader_projection。
        若未指定 checkpoint_id，使用该书最新 checkpoint。
        """
        async with CheckpointManager.connect() as mgr:
            # 确定目标 checkpoint（支持前端直接传 thread_id）
            target_thread_id: Optional[str] = thread_id or None
            target_checkpoint_id: Optional[str] = checkpoint_id
            ts: Optional[str] = None

            if not target_checkpoint_id or (target_thread_id and not target_checkpoint_id):
                cps = await mgr.list_checkpoints(book_id)
                if cps:
                    if target_thread_id:
                        # 该线程的最新一条
                        for c in cps:
                            if c.thread_id == target_thread_id:
                                target_checkpoint_id = c.checkpoint_id
                                ts = c.ts
                                break
                    else:
                        target_checkpoint_id = cps[0].checkpoint_id
                        target_thread_id = cps[0].thread_id
                        ts = cps[0].ts
            if target_checkpoint_id and not target_thread_id:
                cps = await mgr.list_checkpoints(book_id)
                for c in cps:
                    if c.checkpoint_id == target_checkpoint_id:
                        target_thread_id = c.thread_id
                        ts = c.ts
                        break

            if not target_checkpoint_id or not target_thread_id:
                return {
                    "raw_state": {},
                    "reader_projection": {
                        "chapters": [],
                        "stream_buffer": "",
                        "progress": {"steps": 0, "buffer_len": 0, "chapter_count": 0, "writing_state": None},
                    },
                    "meta": {"book_id": book_id, "thread_id": None, "checkpoint_id": None, "ts": None},
                }

            # 读取完整 checkpoint 数据
            cfg = {"configurable": {"thread_id": target_thread_id, "checkpoint_id": target_checkpoint_id}}
            cp_data = await mgr.checkpointer.aget(cfg)  # type: ignore[attr-defined]
            checkpoint = cp_data.checkpoint if hasattr(cp_data, "checkpoint") else cp_data
            values = _ensure_state_dict(checkpoint)

            # 构造 reader_projection
            chapters: List[Dict[str, Any]] = []
            stream_buffer = ""
            steps = int(values.get("steps") or 0)
            writing_state = values.get("writing_state")

            try:
                bd = values.get("book_detail") or {}
                # LangChain 反序列化对象：尽量用 dict 访问
                chs = bd.get("chapters") if isinstance(bd, dict) else []
                if isinstance(chs, list):
                    for item in chs:
                        try:
                            if isinstance(item, dict):
                                number = int(item.get("chapter_number") or 0)
                                title = str(item.get("chapter_title") or "")
                                content = str(item.get("chapter_content") or "")
                            else:
                                # 兜底：对象属性访问
                                number = int(getattr(item, "chapter_number", 0))
                                title = str(getattr(item, "chapter_title", ""))
                                content = str(getattr(item, "chapter_content", ""))
                            if number > 0 and (title or content):
                                chapters.append({"number": number, "title": title, "content": content})
                        except Exception:
                            continue
                # 流式缓冲
                stream_buffer = (
                    str(bd.get("stream_buffer") or "")
                    if isinstance(bd, dict)
                    else str(getattr(bd, "stream_buffer", ""))
                )
            except Exception:
                pass

            # buffer_len：直接基于 stream_buffer 推断
            buffer_len = len(stream_buffer or "")

            reader_projection = {
                "chapters": sorted(chapters, key=lambda x: x["number"]),
                "stream_buffer": stream_buffer,
                "progress": {
                    "steps": steps,
                    "buffer_len": buffer_len,
                    "chapter_count": len(chapters),
                    "writing_state": writing_state,
                },
            }

            return {
                "raw_state": values,
                "reader_projection": reader_projection,
                "meta": {
                    "book_id": book_id,
                    "thread_id": target_thread_id,
                    "checkpoint_id": target_checkpoint_id,
                    "ts": ts,
                },
            }

    async def create_book(self, body: Dict[str, Any]) -> Dict[str, Any]:
        """新建书：透传到 FlowService.run 等价逻辑，但此处轻量直接构建 run 所需参数。
        - 支持 prompt -> llm_input 映射
        - 返回 book_id = thread_id（首次创建时等同 thread_id）
        """
        # 将 prompt 转为 llm_input（若未提供 llm_input）
        llm_input = body.get("llm_input")
        if not llm_input and body.get("prompt"):
            llm_input = {"messages": [{"type": "human", "content": str(body.get("prompt"))}]}

        # 直接调用 LangGraph 流程一次（单步）
        async with AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer:
            await checkpointer.setup()
            from src.langgraph.entrypoint import build_qflow_workflow  # 延迟导入，避免循环

            workflow = build_qflow_workflow(checkpointer)
            # 预生成 thread_id，确保 book_id = 第一次 thread_id
            new_thread_id = str(uuid.uuid4())
            config = {
                "recursion_limit": int(body.get("recursion_limit") or 1),
                "configurable": {
                    "thread_id": new_thread_id,
                    "workspace_path": body.get("workspace_path") or "./workspace",
                    "default_new_project": True,
                    "book_name": body.get("book_name", None),
                },
            }
            # 内核期望的 inputs：传入 llm_input/messages 即可
            inputs = {}
            if llm_input:
                inputs["llm_input"] = llm_input

            result = await workflow.with_config(config).ainvoke(inputs)

            return {
                "book_id": new_thread_id,
                "thread_id": new_thread_id,
                "next": result.get("next") if isinstance(result, dict) else None,
                "steps": int(result.get("steps", 0)) if isinstance(result, dict) else 0,
            }

    async def continue_book(self, book_id: str, body: Dict[str, Any], stream_mode=["values", "message", "updates"]):
        """以 LangGraph astream_events 流式继续写作，逐事件返回。"""
        llm_input = body.get("llm_input")
        if not llm_input and body.get("prompt"):
            llm_input = {"messages": [{"type": "human", "content": str(body.get("prompt"))}]}

        checkpoint_id = body.get("checkpoint_id")
        async with CheckpointManager.connect() as mgr:
            target_thread_id: Optional[str] = None
            if checkpoint_id:
                cps = await mgr.list_checkpoints(book_id)
                for c in cps:
                    if c.checkpoint_id == checkpoint_id:
                        target_thread_id = c.thread_id
                        break
            if not target_thread_id:
                books = await mgr.list_books()
                rec = next((b for b in books if b.book_id == book_id), None)
                target_thread_id = rec.representative_thread_id if rec else None

        async with AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer:
            await checkpointer.setup()
            from src.langgraph.entrypoint import build_qflow_workflow  # 延迟导入

            workflow = build_qflow_workflow(checkpointer)
            config = {
                "recursion_limit": int(body.get("recursion_limit") or 1),
                "configurable": {
                    "thread_id": target_thread_id,
                    "checkpoint_id": checkpoint_id,
                    "workspace_path": body.get("workspace_path") or "./workspace",
                    "default_new_project": False,
                    "book_name": body.get("book_name", None),
                },
            }
            inputs = {}
            if llm_input:
                inputs["llm_input"] = llm_input

            # values: 每个节点触发一次，并行节点合并触发一次；将state返回上来
            # message: human的没有，llm请求是流式的token by token;
            # updates: 返回每个节点的upate内容
            async for event in workflow.astream(inputs, config, stream_mode=stream_mode):
                yield event

    async def get_full_state(self, book_id: str, *, checkpoint_id: Optional[str]) -> Dict[str, Any]:
        """返回完整状态（Everything Projection）— 调试用途，前端不强制使用。"""
        async with CheckpointManager.connect() as mgr:
            tid, cid, ts = await _resolve_thread_and_cp(mgr, book_id, checkpoint_id, None)
            if not tid or not cid:
                return {"raw_state": {}, "projection": {}, "meta": {"book_id": book_id}}

            state = await _load_state(mgr, tid, cid)

            # 提取投影
            plan = state.get("writer_current_plan")
            setting = {
                "refined_book_setting": state.get("refined_book_setting"),
                "refined_book_outlines": state.get("refined_book_outlines"),
            }
            outlines: Dict[str, Any] = {}
            try:
                bd = state.get("book_detail") or {}
                if isinstance(bd, dict):
                    outlines = {"volume_outlines": bd.get("volume_outlines"), "chapters": bd.get("chapters")}
                else:
                    outlines = {
                        "volume_outlines": getattr(bd, "volume_outlines", None),
                        "chapters": getattr(bd, "chapters", None),
                    }
            except Exception:
                outlines = {}

            characters = {"summaries": state.get("character_summaries"), "details": state.get("character_details")}
            scenes = {"scene_design": state.get("scene_design")}

            projection = {
                "plan": plan,
                "setting": setting,
                "outlines": outlines,
                "characters": characters,
                "scenes": scenes,
            }

            return {
                "raw_state": state,
                "projection": projection,
                "meta": {"book_id": book_id, "thread_id": tid, "checkpoint_id": cid, "ts": ts},
            }

    async def get_plan(self, book_id: str, *, checkpoint_id: Optional[str]):
        async with CheckpointManager.connect() as mgr:
            tid, cid, ts = await _resolve_thread_and_cp(mgr, book_id, checkpoint_id, None)
            state = await _load_state(mgr, tid, cid)
            plan = state.get("writer_current_plan") if isinstance(state, dict) else None

            # 优先从 checkpoint state 读取；若缺失，则回退到 WriterStoreManager
            if plan is None:
                try:
                    from src.langgraph.nodes.common.store_manager import WriterStoreManager

                    sm = WriterStoreManager(book_id=book_id)
                    recovered = await sm.get_writer_plan()
                    if recovered is not None:
                        if hasattr(recovered, "model_dump"):
                            plan = recovered.model_dump()
                        else:
                            plan = recovered
                except Exception:
                    plan = None
            else:
                # 标准化为可序列化 dict
                if hasattr(plan, "model_dump"):
                    plan = plan.model_dump()

            return {"plan": plan, "meta": {"book_id": book_id, "thread_id": tid, "checkpoint_id": cid, "ts": ts}}

    async def get_setting(self, book_id: str, *, checkpoint_id: Optional[str]):
        async with CheckpointManager.connect() as mgr:
            tid, cid, ts = await _resolve_thread_and_cp(mgr, book_id, checkpoint_id, None)
            state = await _load_state(mgr, tid, cid)
            return {
                "refined_book_setting": state.get("refined_book_setting"),
                "refined_book_outlines": state.get("refined_book_outlines"),
                "meta": {"book_id": book_id, "thread_id": tid, "checkpoint_id": cid, "ts": ts},
            }

    async def get_outlines(self, book_id: str, *, checkpoint_id: Optional[str]):
        async with CheckpointManager.connect() as mgr:
            tid, cid, ts = await _resolve_thread_and_cp(mgr, book_id, checkpoint_id, None)
            state = await _load_state(mgr, tid, cid)
            bd = state.get("book_detail") or {}
            if not isinstance(bd, dict):
                bd = {
                    "volume_outlines": getattr(state.get("book_detail"), "volume_outlines", None),
                    "chapters": getattr(state.get("book_detail"), "chapters", None),
                }
            return {
                "volume_outlines": bd.get("volume_outlines"),
                "chapters": bd.get("chapters"),
                "meta": {"book_id": book_id, "thread_id": tid, "checkpoint_id": cid, "ts": ts},
            }

    async def get_characters(self, book_id: str, *, checkpoint_id: Optional[str]):
        async with CheckpointManager.connect() as mgr:
            tid, cid, ts = await _resolve_thread_and_cp(mgr, book_id, checkpoint_id, None)
            state = await _load_state(mgr, tid, cid)
            return {
                "summaries": state.get("character_summaries"),
                "details": state.get("character_details"),
                "meta": {"book_id": book_id, "thread_id": tid, "checkpoint_id": cid, "ts": ts},
            }

    async def get_scenes(self, book_id: str, *, checkpoint_id: Optional[str]):
        async with CheckpointManager.connect() as mgr:
            tid, cid, ts = await _resolve_thread_and_cp(mgr, book_id, checkpoint_id, None)
            state = await _load_state(mgr, tid, cid)
            return {
                "scene_design": state.get("scene_design"),
                "meta": {"book_id": book_id, "thread_id": tid, "checkpoint_id": cid, "ts": ts},
            }
