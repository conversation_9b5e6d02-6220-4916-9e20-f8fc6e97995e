"""Define the state structures for the agent."""

from __future__ import annotations

import base64

import lzma

from typing import TypedDict

from langchain_core.load.dump import dumps

from langchain_core.load.load import loads

from langchain_core.messages import AnyMessage

from langgraph.graph import add_messages

from typing_extensions import Annotated

from src.langgraph.nodes.common.book_types import (
    APP_SERIALIZABLE_MAPPINGS,
    BookDetail,
    BookSceneDesign,
    BookSumary,
    CharacterDetailCollection,
    CharacterSummaryCollection,
    WriterPlan,
)


def _merge_dict(left, right):
    left = left or {}
    right = right or {}
    merged = dict(left)
    merged.update(right)
    return merged


class State(TypedDict):
    """State for the agent system, extends MessagesState with next field."""

    messages: Annotated[list[AnyMessage], add_messages]

    think: str = ""

    user_original_request: str = ""
    writer_current_plan: WriterPlan = None
    refined_book_setting: str
    refined_book_outlines: str
    book_id: str = None
    book_name: str = None
    book_detail: BookDetail = None
    # 进展摘要（多层次 L1/L2/L3）
    book_summary: BookSumary = None
    # 场景设计（并发生成/更新）
    scene_design: BookSceneDesign = None
    # 角色设计 - 分离的角色信息
    character_summaries: CharacterSummaryCollection = None
    character_details: CharacterDetailCollection = None

    # 角色并发：已迁移为节点内 @task 并发+聚合，删除 *_expected/*_result_* 等动态键

    # （v2 起移除人审逻辑，跳过跨轮 /skip 策略）

    # 大纲细化进度标记（保留）
    outline_last_volume_number: int

    # 写作状态：IN_SCENE（场景中）/ SWITCHING_SCENE（切换场景中）
    writing_state: str

    @classmethod
    def deserialize_state(self, serialize_content: str) -> dict:
        # 检查是压缩b64还是原始json
        if serialize_content.startswith("B"):
            compressed_content = base64.b64decode(serialize_content[1:])
            decompressed_content = lzma.decompress(compressed_content).decode("utf-8")
            dump_content = decompressed_content
        else:
            dump_content = serialize_content[1:] if serialize_content.startswith("J") else serialize_content

        # 使用 LangChain 的 loads 进行反序列化，添加自定义命名空间
        state = loads(
            dump_content,
            valid_namespaces=["src"],
            additional_import_mappings=APP_SERIALIZABLE_MAPPINGS,
        )
        return state

    @classmethod
    def serialize_state(self, state: State) -> str:
        # 使用 LangChain 的 dumps 进行序列化
        dump_content = dumps(state)
        # lzma压缩
        compressed_content = lzma.compress(dump_content.encode("utf-8"), preset=9)
        # db需要str类型，做b64处理
        base64_content = base64.b64encode(compressed_content).decode("utf-8")

        # 如果压缩后更长则直接使用原始json
        if len(base64_content) < len(dump_content):
            serialize_content = "B" + base64_content
        else:
            serialize_content = "J" + dump_content
        return serialize_content
