from __future__ import annotations

from typing import Any, Optional, Tuple

from src.langgraph.nodes.common.book_types import (
    BookVolumeOutlinesSchema,
    CharacterDetail,
    CharacterSummary,
    CharacterSummaryCollection,
    SegmentedChapterResult,
    VolumeOutline,
    VolumeSceneDesign,
    WriterPlanSchema,
    WriterPlanStepSchema,
)


def make_mock_for_schema(schema_type: type) -> Any:
    """根据数据 Schema 返回一个最小可用的占位对象，用于单元测试/离线运行。
    若无法构造，返回一个空 dict，由调用方自行兜底。
    """
    name = getattr(schema_type, "__name__", "")
    try:
        if name == "WriterPlanSchema":
            steps = [
                WriterPlanStepSchema(
                    step_title="世界观设定", step_description="设定世界观", step_execution_req="请撰写世界观设定"
                ),
                WriterPlanStepSchema(
                    step_title="主角设定", step_description="设定主角", step_execution_req="请撰写主角设定"
                ),
            ]
            return WriterPlanSchema(
                book_name="Mock书",
                book_description="这是一部用于测试的小说简介",
                plan_thought="先规划再执行",
                plan_steps=steps,
            )

        if name == "BookVolumeOutlinesSchema":
            vo = VolumeOutline(volume_number=1, volume_title="第一卷", volume_outline_content="第一卷概述")
            return BookVolumeOutlinesSchema(volume_outlines=[vo])

        if name == "CharacterSummaryCollection":
            items = [
                CharacterSummary(name="主角甲", summary="冷静坚毅"),
                CharacterSummary(name="配角乙", summary="机智忠诚"),
            ]
            return CharacterSummaryCollection(characters=items)

        if name == "CharacterDetail":
            return CharacterDetail(
                name="主角甲",
                appearance="黑发黑瞳",
                abilities="剑术",
                key_interactions=[],
            )

        if name == "SegmentedChapterResult":
            return SegmentedChapterResult(title="第一章", chapter_content="这是正文...", remaining_text="")

        if name == "VolumeOutline":
            return VolumeOutline(volume_number=1, volume_title="第一卷", volume_outline_content="第一卷概述")

        if name == "VolumeSceneDesign":
            return VolumeSceneDesign(volume_number=1, scenes=[], design_completed=True)

        # 通用退化：尝试无参构造
        try:
            return schema_type()
        except Exception:
            return {}
    except Exception:
        return {}


def mock_text(node_name: str) -> Tuple[str, str]:
    """为文本模式返回固定占位字符串。"""
    return (f"[{node_name}] mock content", "")
