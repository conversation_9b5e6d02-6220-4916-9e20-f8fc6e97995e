from __future__ import annotations

from typing import Optional

from langgraph.config import RunnableConfig

from src.langgraph.nodes.common.book_types import BookDetail, Chapter
from src.langgraph.nodes.common.node_kit import save_chapter_to_db
from src.langgraph.state import State


async def writer_chapter_store_node(state: State, config: RunnableConfig) -> dict:
    """
    单节点：章节入库（简化版）。
    - 输入：`book_id`、`book_detail`、可选 `chapter_store_expected_numbers`
    - 行为：遍历期望章节，检测存在性并写入结果键 `chapter_store_result_<n>`；统一返回 `__end__`
    - 说明：为降低复杂度，此节点不再维护并发/重派发队列；需要并行可由上层多次调用或后续扩展。
    """
    book_id: Optional[str] = state.get("book_id")
    book_detail: Optional[BookDetail] = state.get("book_detail")

    # 期望集合：优先读取 state 指定，否则以现有章节集合为准
    expected_numbers: set[int] = set()
    try:
        raw = state.get("chapter_store_expected_numbers")
        if raw:
            for n in raw:
                try:
                    expected_numbers.add(int(n))
                except Exception:
                    continue
    except Exception:
        expected_numbers = set()

    if not expected_numbers and book_detail and getattr(book_detail, "chapters", None):
        try:
            expected_numbers = {
                int(getattr(ch, "chapter_number", 0))
                for ch in book_detail.chapters
                if getattr(ch, "chapter_number", 0) > 0
            }
        except Exception:
            expected_numbers = set()

    updates: dict = {}
    completed = 0
    total = len(expected_numbers)

    # 索引章节
    chapter_map: dict[int, Chapter] = {}
    if book_detail and getattr(book_detail, "chapters", None):
        try:
            for ch in book_detail.chapters:
                try:
                    n = int(getattr(ch, "chapter_number", 0))
                    if n > 0:
                        chapter_map[n] = ch
                except Exception:
                    continue
        except Exception:
            chapter_map = {}

    for n in sorted(expected_numbers):
        key = f"chapter_store_result_{n}"
        ch = chapter_map.get(n)
        if ch is None:
            updates[key] = {"ok": False, "error": "chapter_not_found"}
            continue
        # 实际入库（失败被 NodeKit 内部吞掉并记录 warning，这里仍按成功计数）
        try:
            await save_chapter_to_db(ch, book_id)
            updates[key] = {"ok": True}
        except Exception as e:
            # 理论上不会抛出，但稳健起见
            updates[key] = {"ok": False, "error": str(e)}
        completed += 1

    # 清理旧的并发状态键（若存在则置空）
    for k in (
        "chapter_store_pending_numbers",
        "chapter_store_dispatched_numbers",
        "chapter_store_dispatch_times",
        "chapter_store_requeue_counts",
        "chapter_store_inflight_count",
        "chapter_store_inflight_preview",
    ):
        if k in state:
            updates[k] = [] if k.endswith("numbers") or k.endswith("preview") else {}

    # 下一步：允许通过 state 指定后续节点，默认 __end__
    try:
        desired_next = state.get("chapter_store_next_node") or "__end__"
        if desired_next not in ("__end__", "stream_write", "chapter_segment"):
            desired_next = "__end__"
    except Exception:
        desired_next = "__end__"

    return updates


__all__ = [
    "writer_chapter_store_node",
]
