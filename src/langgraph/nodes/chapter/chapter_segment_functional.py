from langgraph.config import RunnableConfig

from src.langgraph.context.context_builder import ContextBuilder
from src.langgraph.nodes.chapter.stream_write_prompt import segment_prompt_template
from src.langgraph.nodes.common.book_types import BookDetail, Chapter, SegmentedChapterResult
from src.langgraph.nodes.common.node_kit import llm_json, rel_path_for_chapter, write_text_under_book
from src.langgraph.state import State


def _build_segment_messages(state: State, config: RunnableConfig) -> list:
    book_detail: BookDetail = state.get("book_detail")
    min_len = config.get("configurable", {}).get("segment_min_len", 3000)
    max_len = config.get("configurable", {}).get("segment_max_len", 5000)

    cb = ContextBuilder()
    cb.header()
    # 计算缓冲与长度
    buffer_text = book_detail.stream_buffer if book_detail and book_detail.stream_buffer else ""
    cb.section(
        "智能分章任务",
        segment_prompt_template.format(
            book_name=(
                state.get("writer_current_plan").book_name
                if state.get("writer_current_plan")
                else state.get("book_name") or ""
            ),
            buffer_text=buffer_text,
            min_len=min_len,
            max_len=max_len,
            len=len(buffer_text),
        ),
    )
    cb.json_schema(SegmentedChapterResult.model_json_schema())
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="chapter_segment")


from langgraph.func import task


@task
async def chapter_segment_fn(state: State, config: RunnableConfig) -> dict:
    # 前置检查
    book_detail: BookDetail | None = state.get("book_detail")
    if not book_detail or not book_detail.stream_buffer:
        raise AssertionError("❌ 缓冲为空，无法分章")

    # 构建消息并请求 LLM（结构化）
    messages = _build_segment_messages(state, config)
    seg: SegmentedChapterResult = await llm_json("chapter_segment", messages, SegmentedChapterResult, config=config)

    # 写入章节编号并追加（仅处理正文，智能分章职责收敛）
    next_index = (len(book_detail.chapters) + 1) if book_detail and book_detail.chapters else 1
    ch = Chapter(
        chapter_number=next_index,
        chapter_title=seg.title,
        chapter_content=seg.chapter_content,
    )
    book_detail.chapters.append(ch)

    # 使用 LLM 返回的剩余文本回灌缓冲
    remaining = (seg.remaining_text or "").lstrip()
    book_detail.stream_buffer = remaining

    # 写入章节文件
    file_rel = rel_path_for_chapter(next_index, ch.chapter_title)
    write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, ch.get_completed_content())

    # 决定下一步：进入 prepare_next_chapter，由其决定是否切换场景/继续写作
    update = {"book_detail": book_detail}
    return update


__all__ = ["chapter_segment_fn"]
