from typing import Optional

from langchain_core.messages import BaseMessage
from langgraph.config import RunnableConfig

from src.langgraph.context.context_builder import ContextBuilder
from src.langgraph.nodes.common.book_types import (
    BookDetail,
    BookSceneDesign,
    CharacterDetailCollection,
    VolumeSceneDesign,
    WriterPlan,
)
from src.langgraph.nodes.common.node_kit import write_text_under_book
from src.langgraph.nodes.common.store_manager import WriterStoreManager
from src.langgraph.nodes.scene.scene_prompt import smart_volume_scene_design_template, volume_scene_design_template
from src.langgraph.nodes.scene.scene_tasks import scene_design_task
from src.langgraph.state import State


def _get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    if book_detail is None or not getattr(book_detail, "chapters", None):
        return 1
    return len(book_detail.chapters) + 1


def _build_scene_messages(
    state: State, config: RunnableConfig, next_volume: int, current_volume_outline_text: str
) -> list[BaseMessage]:
    refined_book_setting = state.get("refined_book_setting")
    character_details: CharacterDetailCollection = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""

    enable_smc = config.get("configurable", {}).get("enable_smart_character_management", True)
    scene_prompt = (smart_volume_scene_design_template if enable_smc else volume_scene_design_template).format(
        volume_number=next_volume,
        refined_book_setting=refined_book_setting,
        character_design=character_content,
        volume_outline=current_volume_outline_text,
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("场景设计任务", scene_prompt)
    cb.json_schema(VolumeSceneDesign.model_json_schema())

    demo_chapter = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo_chapter)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="scene")


from langgraph.func import task


@task
async def writer_scene_fn(state: State, config: RunnableConfig) -> dict:
    plan: WriterPlan | None = state.get("writer_current_plan")
    book_detail: BookDetail | None = state.get("book_detail")
    book_id = state.get("book_id")
    book_name = state.get("book_name")
    character_summaries = state.get("character_summaries")
    character_details: CharacterDetailCollection | None = state.get("character_details")

    # 前置检查（与旧实现保持一致）
    if not plan or not plan.is_all_step_completed():
        return {}
    if not plan.is_all_step_completed():
        return {}
    if book_id is None:
        return {}
    if character_summaries is None or not getattr(character_summaries, "characters", None):
        return {}
    if character_details is None:
        return {}
    if len(character_details.characters) != len(character_summaries.characters):
        return {}
    if book_detail is None:
        return {}

    current_chapter = _get_current_chapter_number(book_detail)
    if not book_detail.is_outline_ready_for_writing(current_chapter):
        return {}

    # 初始化场景设计结构（仅一次）
    if state.get("scene_design") is None:
        volume_scenes = [
            VolumeSceneDesign(volume_number=vo.volume_number, scenes=[], design_completed=False)
            for vo in (book_detail.volume_outlines or [])
        ]
        scene_design = BookSceneDesign(volume_scenes=volume_scenes)
        update = {"scene_design": scene_design}

        return update

    # 并发细化：从当前卷开始到后续2卷内所有未完成的卷
    scene_design: BookSceneDesign = state.get("scene_design")
    current_volume_number = (current_chapter - 1) // 100 + 1
    max_required_volume = current_volume_number + 2

    targets: list[int] = []
    for vol in book_detail.volume_outlines:
        if current_volume_number <= vol.volume_number <= max_required_volume:
            vs = scene_design.get_volume_scenes(vol.volume_number)
            if not vs or not vs.design_completed:
                targets.append(vol.volume_number)

    if not targets:
        # 若无目标卷需要细化：检查“待办卷”数量；若 <2 则补充到 2 卷（逐卷触发）
        try:
            desired_next_vol = state.get("outline_generate_next_volume_number")
        except Exception:
            desired_next_vol = None

        # 计算当前卷之后的已存在卷纲数量
        try:
            upcoming_count = len(
                [vo for vo in (book_detail.volume_outlines or []) if vo.volume_number > current_volume_number]
            )
            max_vol = max([vo.volume_number for vo in (book_detail.volume_outlines or [])] or [current_volume_number])
        except Exception:
            upcoming_count = 0
            max_vol = current_volume_number

        if upcoming_count < 2 and desired_next_vol is None:
            next_volume_number = max_vol + 1
            update = {"outline_generate_next_volume_number": next_volume_number}
            return update
        return {}

    def _find_outline(vol_number: int):
        for item in book_detail.volume_outlines:
            if item.volume_number == vol_number:
                return item
        return None

    # 节点内使用 @task 并发设计场景并直接聚合
    update = {
        "scene_task_current_volume": current_volume_number,
        "scene_task_max_volume": max_required_volume,
    }

    # 构造每个任务 payload
    character_content = character_details.get_characters_content() if character_details else ""
    futures = []
    cfg = {}
    try:
        cfg = config.get("configurable", {})
    except Exception:
        try:
            cfg = getattr(config, "configurable", {}) or {}
        except Exception:
            cfg = {}

    def _get_outline_text(vnum: int) -> str:
        vo = None
        for item in book_detail.volume_outlines or []:
            if item.volume_number == vnum:
                vo = item
                break
        return vo.get_completed_contents() if vo else ""

    for vol_number in targets:
        # 计算该卷已存在的场景数
        existing_vs = scene_design.get_volume_scenes(vol_number) if scene_design else None
        existing_count = len(existing_vs.scenes) if existing_vs and existing_vs.scenes else 0
        need_append = existing_count < 2  # 规则：待办场景<2则补到2
        target_needed = max(0, 2 - existing_count)

        payload = {
            "volume_number": vol_number,
            "refined_book_setting": state.get("refined_book_setting", ""),
            "character_content": character_content,
            "current_volume_outline_text": _get_outline_text(vol_number),
            "book_name": book_name,
            "book_id": book_id,
            "configurable": cfg,
        }
        if need_append and target_needed > 0:
            payload.update(
                {
                    "append_mode": True,
                    "existing_scenes_count": existing_count,
                    "target_needed_scenes": target_needed,
                }
            )
        futures.append(scene_design_task(payload))

    # 收集结果（优先 .result()；失败则 await）
    results = []
    for f in futures:
        try:
            results.append(f.result())
        except Exception:
            try:
                results.append(await f)
            except Exception as e:
                results.append({"error": str(e)})

    # 应用结果
    scene_design: BookSceneDesign = state.get("scene_design")
    batch_lines: list[str] = [
        "# 场景设计批量更新",
        "",
        f"范围：第{current_volume_number}~{max_required_volume}卷",
        "",
        "---",
        "",
    ]
    last_vol: int | None = None
    for r in results:
        try:
            vol = int(r.get("volume_number"))
            refined_dump = r.get("refined")
            if not refined_dump:
                continue
            refined = VolumeSceneDesign.model_validate(refined_dump)
            exist = scene_design.get_volume_scenes(vol)
            if exist:
                exist.scenes = refined.scenes
                exist.design_completed = True
            else:
                scene_design.volume_scenes.append(refined)
            last_vol = vol
            batch_lines.append(f"## 第{vol}卷 场景清单")
            batch_lines.append("")
            batch_lines.append(refined.get_scenes_content())
            batch_lines.append("")
        except Exception:
            continue

    batch_file_rel = f"scene/批量-第{current_volume_number}~{max_required_volume}卷-场景设计.md"
    review_file = write_text_under_book(book_name, book_id, batch_file_rel, "\n".join(batch_lines))

    # 入库（一次）
    try:
        if book_id:
            sm = WriterStoreManager(book_id=book_id)
            await sm.store_scene_design(scene_design)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    update.update({"scene_design": scene_design, "scene_last_volume": last_vol})

    return update


__all__ = ["writer_scene_fn"]
