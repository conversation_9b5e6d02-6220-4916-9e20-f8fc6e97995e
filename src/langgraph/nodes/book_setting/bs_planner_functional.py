from langchain_core.messages import BaseMessage

from langgraph.config import RunnableConfig

from src.langgraph.context.context_builder import ContextBuilder
from src.langgraph.nodes.book_setting.bs_planner_prompt import base_prompt_template, plan_template
from src.langgraph.nodes.common.book_types import WriterPlanSchema
from src.langgraph.nodes.common.node_kit import llm_json, save_plan_to_db, write_text_under_book
from src.langgraph.state import State


def _build_messages(state: State, config: RunnableConfig) -> list[BaseMessage]:
    cb = ContextBuilder()
    cb.header()
    cb.section("创作计划任务", base_prompt_template.format())

    book_name = config.get("configurable", {}).get("book_name")
    if book_name is not None:
        cb.section("书名", f"书名使用《{book_name}》")

    cb.demo(config.get("configurable", {}).get("demo_chapter"))
    cb.demo(config.get("configurable", {}).get("demo_bs"))

    cb.section("计划格式", plan_template.format(max_step_num=5))
    cb.json_schema(WriterPlanSchema.model_json_schema())

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    messages = cb.build(max_tokens=budget, policy="bs_planner")
    messages.extend(state.get("messages", []))
    return messages


from langgraph.func import task


@task
async def bs_planner_fn(state: State) -> dict:
    # 已存在策划案：作为恢复入口直接跳过 LLM，由入口控制流决定下一步
    existing_plan = state.get("writer_current_plan")
    if existing_plan is not None:
        return {}

    # 1) 构建输入
    # 在函数式入口移除了全局 get_config，这里按 state.configurable 约定
    messages = _build_messages(state, RunnableConfig(configurable=state.get("configurable") or {}))

    # 2) 调用 LLM（JSON）
    parsed = await llm_json(
        "bs_planner", messages, WriterPlanSchema, config={"configurable": state.get("configurable") or {}}
    )
    plan = parsed.convert_to_writer_plan()

    # 3) 写基础状态（book_id=thread_id, book_name）与用户原始诉求
    thread_id = (state.get("configurable") or {}).get("thread_id")
    if not thread_id:
        raise AssertionError("config.configurable.thread_id 未设置，无法作为 book_id")
    book_id = thread_id
    book_name = plan.book_name

    user_original_request = ""
    for msg in state.get("messages", []):
        if getattr(msg, "type", None) == "human":
            user_original_request = msg.content
            break

    update = {
        "book_name": book_name,
        "book_id": book_id,
        "writer_current_plan": plan,
        "user_original_request": user_original_request,
    }

    # 4) 落盘与入库
    file_rel = "setting/0-setting_plan.md"
    _ = write_text_under_book(book_name, book_id, file_rel, plan.get_completed_contents())
    await save_plan_to_db(plan, book_id)

    return update
