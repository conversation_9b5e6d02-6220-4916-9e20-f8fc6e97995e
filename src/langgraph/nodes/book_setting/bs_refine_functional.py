from langgraph.config import RunnableConfig

from src.langgraph.context.context_builder import ContextBuilder
from src.langgraph.nodes.book_setting.bs_steps_prompt import bs_refine_prompt_template
from src.langgraph.nodes.common.book_types import WriterP<PERSON>, WriterPlanStep
from src.langgraph.nodes.common.node_kit import llm_text, write_text_under_book
from src.langgraph.nodes.common.store_manager import WriterStoreManager
from src.langgraph.state import State


def _select_refine_target(state: State) -> tuple[int | None, WriterPlan | None, WriterPlanStep | None, str | None]:
    plan: WriterPlan | None = state.get("writer_current_plan")
    if plan is None:
        return None, None, None, None

    refine_id = None
    refine_step: WriterPlanStep | None = None
    refine_contents: str | None = None

    try:
        refine_id, refine_step = next(
            (idx, step) for idx, step in enumerate(plan.plan_steps) if step.needs_refinement()
        )
        refine_contents = refine_step.get_step_content()
    except StopIteration:
        return None, plan, None, None

    return refine_id, plan, refine_step, refine_contents


def _build_messages(state: State, config: RunnableConfig, refine_contents: str) -> list:
    cb = ContextBuilder()
    cb.header()
    cb.section(
        "设定精炼任务",
        bs_refine_prompt_template.format(
            user_original_request=state.get("user_original_request"),
            refined_book_setting=state.get("refined_book_setting", "暂无"),
            new_contents=refine_contents,
        ),
    )
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="bs_steps")


from langgraph.func import task


@task
async def bs_refine_fn(state: State) -> dict:
    refine_id, plan, refine_step, refine_contents = _select_refine_target(state)

    # 无计划或无可精炼项 → 返回空更新，由入口控制流推进
    if plan is None or refine_step is None:
        return {}

    # 构建消息
    messages = _build_messages(
        state, RunnableConfig(configurable=state.get("configurable") or {}), refine_contents or ""
    )

    # 生成文本
    content, _ = await llm_text("bs_refine", messages, config={"configurable": state.get("configurable") or {}})

    # 计算写入的 refined_book_setting 文本（保持与旧实现一致）
    refined_book_setting = refine_contents if refine_id == 0 else content

    # 标记步骤为已精炼
    try:
        refine_step.set_status("refined")
    except Exception:
        pass

    # 入库
    try:
        if state.get("book_id"):
            sm = WriterStoreManager(book_id=state.get("book_id"))
            await sm.store_refined_book_setting(refined_book_setting)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    # 落盘
    file_rel = f"setting/0.{refine_id}-refined-booksetting.md"
    _ = write_text_under_book(state.get("book_name"), state.get("book_id"), file_rel, refined_book_setting)

    # 如果所有步骤都已完成，清理上下文
    update: dict = {
        "refined_book_setting": refined_book_setting,
        "writer_current_plan": plan,
    }

    return update


__all__ = ["bs_refine_fn"]
