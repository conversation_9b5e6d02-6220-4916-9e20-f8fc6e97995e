from __future__ import annotations

from typing import Optional

from langgraph.config import RunnableConfig

from src.langgraph.context.context_builder import ContextBuilder
from src.langgraph.nodes.character.character_prompt import core_character_design_template
from src.langgraph.nodes.character.character_tasks import character_detail_task
from src.langgraph.nodes.common.book_types import (
    CharacterDetail,
    CharacterDetailCollection,
    CharacterSummaryCollection,
    WriterPlan,
)
from src.langgraph.nodes.common.node_kit import llm_json, write_text_under_book
from src.langgraph.state import State


def _build_summary_messages(state: State, config: RunnableConfig) -> list:
    plan: WriterPlan = state.get("writer_current_plan")
    cb = ContextBuilder()
    cb.header()
    cb.section(
        "角色设计任务",
        core_character_design_template.format(
            refined_book_setting=state.get("refined_book_setting", "暂无"),
            book_name=plan.book_name if plan else (state.get("book_name") or ""),
            book_description=(plan.book_description if plan else "") or "",
        ),
    )
    cb.json_schema(CharacterSummaryCollection.model_json_schema())
    demo = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo)
    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="character")


def _build_detail_messages(*args, **kwargs):  # 兼容旧引用 - 保留占位以避免误用
    raise RuntimeError(
        "_build_detail_messages 已废弃，请使用 src/nodes/character/character_tasks.py::_build_detail_messages"
    )


async def _ensure_summaries(
    state: State, config: RunnableConfig
) -> tuple[Optional[CharacterSummaryCollection], dict, str]:
    summaries: Optional[CharacterSummaryCollection] = state.get("character_summaries")
    update: dict = {}

    if summaries is None or not getattr(summaries, "characters", None):
        messages = _build_summary_messages(state, config)
        summaries = await llm_json(
            "writer_character:init",
            messages,
            CharacterSummaryCollection,
            config={"configurable": state.get("configurable") or {}},
        )
        # 兜底：若 mock 返回 dict，则强转为模型
        if isinstance(summaries, dict):
            try:
                summaries = CharacterSummaryCollection.model_validate(summaries)
            except Exception:
                pass

        # 落盘
        book_name = state.get("book_name") or (
            state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
        )
        book_id = state.get("book_id")
        rel = "character/角色简介.md"
        try:
            content = summaries.get_characters_content() if hasattr(summaries, "get_characters_content") else ""
            _ = write_text_under_book(book_name, book_id, rel, content)
        except Exception:
            pass

        update["character_summaries"] = summaries

    next_node = "writer_character"
    return summaries, update, next_node


async def _ensure_details_concurrent(
    state: State, config: RunnableConfig, summaries: CharacterSummaryCollection
) -> tuple[Optional[CharacterDetailCollection], dict, str]:

    details: Optional[CharacterDetailCollection] = state.get("character_details")
    update: dict = {}
    default_next = "writer_outline"

    done_names = set([ch.name for ch in details.characters]) if details and details.characters else set()
    targets = [s.name for s in summaries.characters if s.name not in done_names]
    if not targets:
        return details, update, default_next

    # 构造每个任务的 payload（显式传递上下文，避免并发读空）
    cfg = {}
    try:
        cfg = config.get("configurable", {})  # RunnableConfig 兼容 dict.get
    except Exception:
        try:
            cfg = getattr(config, "configurable", {}) or {}
        except Exception:
            cfg = {}

    # 为每个目标构建 target_summary / others
    name_to_summary = {s.name: s.get_full_content() for s in summaries.characters}
    all_contents = {s.name: s.get_full_content() for s in summaries.characters}

    book_name = state.get("book_name") or (
        state.get("writer_current_plan").book_name if state.get("writer_current_plan") else ""
    )
    book_id = state.get("book_id")
    refined_book_setting = state.get("refined_book_setting", "暂无")
    plan = state.get("writer_current_plan")
    plan_book_name = plan.book_name if plan else (state.get("book_name") or "")
    plan_book_description = (plan.book_description if plan else "") or ""

    futures = []
    for name in targets:
        payload = {
            "refined_book_setting": refined_book_setting,
            "plan_book_name": plan_book_name,
            "plan_book_description": plan_book_description,
            "character_name": name,
            "target_summary": name_to_summary.get(name, ""),
            "other_characters": "\n\n".join([v for k, v in all_contents.items() if k != name]),
            "book_name": book_name,
            "book_id": book_id,
            "configurable": cfg,
        }
        futures.append(character_detail_task(payload))

    # 收集结果（等待所有任务完成）
    # LangGraph @task 支持 .result()；但在某些运行环境下需确保任务完成
    results = []
    for f in futures:
        try:
            results.append(f.result())
        except Exception:
            # 退一步：直接 await 任务以确保完成
            try:
                results.append(await f)
            except Exception as e:
                # 记录占位错误，后续聚合时丢弃
                results.append({"error": str(e)})

    # 聚合与校验
    expected = set(targets)
    merged = list(details.characters) if details and details.characters else []
    exist_map = {ch.name: idx for idx, ch in enumerate(merged)}

    invalid_samples = []
    for r in results:
        try:
            name = r.get("name")
            detail_dump = r.get("detail")
            if not name or name not in expected or not isinstance(detail_dump, dict):
                invalid_samples.append(str(name))
                continue
            detail = CharacterDetail.model_validate(detail_dump)
            # 再兜底一次
            if not getattr(detail, "name", None) or getattr(detail, "name") != name:
                setattr(detail, "name", name)
            if detail.name in exist_map:
                merged[exist_map[detail.name]] = detail
            else:
                merged.append(detail)
        except Exception:
            invalid_samples.append(str(r))
            continue

    # 仅保留 summaries 中的角色顺序
    order_map = {s.name: i for i, s in enumerate(summaries.characters)}
    merged_filtered = []
    seen = set()
    for ch in merged:
        if ch.name in order_map and ch.name not in seen:
            merged_filtered.append(ch)
            seen.add(ch.name)

    # 写盘（按顺序覆盖一遍）
    for ch in merged_filtered:
        idx = order_map.get(ch.name, 0) + 1
        pad = "0" if idx < 10 else ""
        rel = f"character/角色详情_{pad}{idx}_{ch.name}.md"
        write_text_under_book(book_name, book_id, rel, f"# {ch.get_full_content()}\n")

    new_details = CharacterDetailCollection(characters=merged_filtered)

    # 汇总与审阅
    summary_rel = "character/角色详情.md"
    _ = write_text_under_book(book_name, book_id, summary_rel, new_details.get_characters_content())

    update["character_details"] = new_details

    # 人工审核已移除，直接下一步
    next_node = default_next

    return new_details, update, next_node


from langgraph.func import task


@task
async def writer_character_fn(state: State, config: RunnableConfig) -> dict:
    # 0) 基本前置
    plan: WriterPlan | None = state.get("writer_current_plan")
    assert plan.is_all_step_completed(), "❌ 策划案未完成或未精炼，无法创建角色设计"

    # 1) 简介（缺则生成，一次 interrupt 审核）
    summaries, update1, next1 = await _ensure_summaries(state, config)
    update: dict = dict(update1)
    next_node = next1

    # 若被中断且未通过，直接返回等待继续
    if next_node == "writer_character" and not update.get("character_details"):
        # 保持停留在本节点，等待 join 或审阅通过
        pass

    if summaries is None or not getattr(summaries, "characters", None):
        return update

    # 2) 详情（节点内部用 @task 并发+聚合）
    _res = await _ensure_details_concurrent(state, config, summaries)
    update2, next2 = _res[1], _res[2]
    update.update(update2)
    next_node = next2

    return update


__all__ = ["writer_character_fn"]
