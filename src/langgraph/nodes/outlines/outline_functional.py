import logging
from typing import Optional

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.config import RunnableConfig

from src.langgraph.context.context_builder import ContextBuilder
from src.langgraph.nodes.common.book_types import (
    BookDetail,
    BookVolumeOutlinesSchema,
    CharacterDetailCollection,
    VolumeOutline,
    WriterPlan,
)
from src.langgraph.nodes.common.node_kit import llm_json, write_text_under_book
from src.langgraph.nodes.common.store_manager import WriterStoreManager
from src.langgraph.nodes.outlines.outline_prompt import bo_init_prompt_template
from src.langgraph.nodes.outlines.outline_tasks import outline_refine_task
from src.langgraph.state import State


logger = logging.getLogger(__name__)


def _get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    if book_detail is None or not getattr(book_detail, "chapters", None):
        return 1
    return len(book_detail.chapters) + 1


def _build_init_messages(state: State, config: RunnableConfig) -> list[BaseMessage]:
    writer_current_plan: WriterPlan = state.get("writer_current_plan")
    character_details: CharacterDetailCollection = state.get("character_details")

    character_content = character_details.get_characters_content() if character_details else ""
    volum_outlines_prompt = bo_init_prompt_template.format(
        book_name=writer_current_plan.book_name,
        book_description=writer_current_plan.book_description,
        refined_book_setting=state.get("refined_book_setting"),
        character_design=character_content,
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("大纲任务", volum_outlines_prompt)
    cb.json_schema(BookVolumeOutlinesSchema.model_json_schema())

    demo_chapter = config.get("configurable", {}).get("demo_chapter")
    cb.demo(demo_chapter)

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="outline")


def _build_next_volume_messages(state: State, config: RunnableConfig, next_volume_number: int) -> list[BaseMessage]:
    character_details: CharacterDetailCollection = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""

    # 汇总已存在卷大纲文本作为参考
    book_detail: BookDetail | None = state.get("book_detail")
    existing_outlines = book_detail.get_completed_volume_outlines() if book_detail else ""

    prompt_text = (
        f"请基于设定与已完成卷纲，为第{next_volume_number}卷生成卷纲。\n"
        f"要求：符合爽文风格，提供卷标题、核心矛盾、推进目标与关键事件串。仅输出 JSON 符合 VolumeOutline。"
    )

    cb = ContextBuilder()
    cb.header()
    cb.section("设定集", state.get("refined_book_setting"))
    cb.section("角色设计", character_content)
    if existing_outlines:
        cb.section("已完成卷纲参考", existing_outlines)
    cb.section("生成任务", prompt_text)
    cb.json_schema(VolumeOutline.model_json_schema())

    budget = config.get("configurable", {}).get("prompt_budget_tokens")
    return cb.build(max_tokens=budget, policy="outline")


from langgraph.func import task


@task
async def writer_outline_fn(state: State, config: RunnableConfig) -> dict:
    plan: WriterPlan | None = state.get("writer_current_plan")
    book_id = state.get("book_id")
    book_name = state.get("book_name")
    character_summaries = state.get("character_summaries")
    character_details: CharacterDetailCollection | None = state.get("character_details")
    book_detail: BookDetail | None = state.get("book_detail")

    # 前置检查：对齐旧实现
    assert plan is not None and plan.is_all_step_completed(), "plan 不存在或步骤未完成"
    assert book_id is not None, "book_id 不存在"
    assert character_summaries is not None and getattr(
        character_summaries, "characters", None
    ), "character_summaries 不存在或无角色"
    assert character_details is not None, "character_details 不存在"
    # 若 summaries 与 details 数量不一致，日志警告
    expected = [s.name for s in (character_summaries.characters or [])]
    have = [ch.name for ch in (character_details.characters or [])]
    if len(have) != len(expected):
        logger.error(f"character_details and character_summaries 数量不一致: {have} != {expected}")

    # 若外部指明需要生成下一卷（增量），优先处理
    try:
        desired_next_vol = state.get("outline_generate_next_volume_number")
    except Exception:
        desired_next_vol = None

    if desired_next_vol is not None:
        next_vol_num = int(desired_next_vol)
        messages = _build_next_volume_messages(state, config, next_vol_num)
        parsed: VolumeOutline = await llm_json("writer_outline:next_volume", messages, VolumeOutline, config=config)
        if not parsed or not parsed.volume_title:
            raise AssertionError("❌ 生成下一卷失败：结果为空或缺少标题")

        # 追加到现有 book_detail
        if book_detail is None:
            book_detail = BookDetail(volume_outlines=[parsed])
        else:
            book_detail.volume_outlines.append(parsed)

        # 落盘与入库
        file_rel = f"outline/第{parsed.volume_number}卷-大纲.md"
        _ = write_text_under_book(book_name, book_id, file_rel, parsed.get_completed_contents())
        try:
            if book_id:
                sm = WriterStoreManager(book_id=book_id)
                await sm.store_volume_outline(book_detail)
        except RuntimeError as e:
            if "Called get_config outside of a runnable context" not in str(e):
                raise

        update = {"book_detail": book_detail, "outline_generate_next_volume_number": None}

        return update

    # 阶段判定
    current_chapter = _get_current_chapter_number(book_detail)
    if book_detail is None or not book_detail.volume_outlines:
        stage = "init_volume_outlines"
    elif getattr(
        book_detail, "need_volume_outline_refinement_for_current_progress", None
    ) and book_detail.need_volume_outline_refinement_for_current_progress(current_chapter):
        stage = "refine_volume_outlines"
    else:
        stage = "completed"

    # 已完成 → 进入下游
    if stage == "completed":
        return {}

    # 初始化阶段
    if stage == "init_volume_outlines":
        messages = _build_init_messages(state, config)
        # 稳健性：异常→自动重试（一次）→ 人审
        try:
            parsed = await llm_json("writer_outline", messages, BookVolumeOutlinesSchema, config=config)
            if not getattr(parsed, "volume_outlines", None):
                raise ValueError("empty_volume_outlines")
        except Exception as e:
            # 一次回退重试：追加提示并可选升级 tier
            retry_messages = list(messages)
            retry_messages.append(
                HumanMessage(
                    name="review",
                    content="请严格输出有效 JSON，符合给定 Schema（不得包含注释/示例文字/占位），并确保非空的分卷大纲列表。",
                )
            )
            try:
                parsed = await llm_json(
                    "writer_outline",
                    retry_messages,
                    BookVolumeOutlinesSchema,
                    config=config,
                    stage_name="retry",
                    tier_override="high",
                )
                if not getattr(parsed, "volume_outlines", None):
                    raise ValueError("empty_volume_outlines_retry")
            except Exception as e2:
                book_id = state.get("book_id")
                book_name = state.get("book_name")
                error_text = f"大纲初始化失败：{str(e2) or str(e)}\n\n请在此文件中直接提供分卷大纲草案或意见，或修改后输入 /pass 继续。"
                _ = write_text_under_book(book_name, book_id, "outline/整书分卷大纲-待审.md", error_text)

                return {}

        book_detail_new = BookDetail(volume_outlines=parsed.volume_outlines)

        # 落盘
        file_rel = "outline/整书分卷大纲.md"
        _ = write_text_under_book(book_name, book_id, file_rel, book_detail_new.get_completed_volume_outlines())

        # 入库
        try:
            if book_id:
                sm = WriterStoreManager(book_id=book_id)
                await sm.store_volume_outline(book_detail_new)
        except RuntimeError as e:
            if "Called get_config outside of a runnable context" not in str(e):
                raise

        update = {"book_detail": book_detail_new}

        return update

    # 细化阶段（并发）：基于当前章节进度细化当前卷到后续2卷内所有 version==0 的卷
    # 调试期：断言 book_detail/volume_outlines 存在
    assert book_detail is not None and getattr(
        book_detail, "volume_outlines", None
    ), "[ASSERT] writer_outline_fn: 进入细化阶段但 book_detail 缺失或无 volume_outlines"

    current_volume_number = (current_chapter - 1) // 100 + 1
    max_required_volume = current_volume_number + 2

    target_indices: list[int] = []
    for i, vol in enumerate(book_detail.volume_outlines):
        if vol.version == 0 and current_volume_number <= vol.volume_number <= max_required_volume:
            target_indices.append(i)

    if not target_indices:
        return {}

    # 节点内用 @task 并发细化并直接聚合
    character_details: CharacterDetailCollection | None = state.get("character_details")
    character_content = character_details.get_characters_content() if character_details else ""
    refined_book_outlines_text = book_detail.get_completed_volume_outlines()

    cfg = {}
    try:
        cfg = config.get("configurable", {})
    except Exception:
        try:
            cfg = getattr(config, "configurable", {}) or {}
        except Exception:
            cfg = {}

    futures = []
    for i in target_indices:
        vol = book_detail.volume_outlines[i]
        payload = {
            "refined_book_setting": state.get("refined_book_setting"),
            "character_content": character_content,
            "refined_book_outlines": refined_book_outlines_text,
            "volume_index": i,
            "volume_number": vol.volume_number,
            "volume_title": vol.volume_title,
            "version": getattr(vol, "version", 0),
            "book_name": book_name,
            "book_id": book_id,
            "configurable": cfg,
        }
        futures.append(outline_refine_task(payload))

    # 收集结果（优先 .result()；失败则 await 以确保完成）
    results = []
    for f in futures:
        try:
            results.append(f.result())
        except Exception:
            try:
                results.append(await f)
            except Exception as e:
                results.append({"error": str(e)})

    # 应用结果回写到 book_detail
    for r in results:
        try:
            idx = int(r.get("index"))
            refined_dump = r.get("refined")
            if not isinstance(refined_dump, dict):
                continue
            refined = VolumeOutline.model_validate(refined_dump)
            book_detail.volume_outlines[idx] = refined
        except Exception:
            continue

    # 批量文件
    lines = [
        "# 卷细化批量更新",
        "",
        f"范围：第{current_volume_number}~{max_required_volume}卷",
        "",
        "---",
        "",
    ]
    for i in sorted(target_indices, key=lambda j: book_detail.volume_outlines[j].volume_number):
        refined = book_detail.volume_outlines[i]
        lines.append(f"## 第{refined.volume_number}卷 {refined.volume_title}")
        lines.append("")
        lines.append(refined.get_completed_contents())
        lines.append("")
    batch_content = "\n".join(lines)
    batch_file_rel = f"outline/批量-第{current_volume_number}~{max_required_volume}卷-细化大纲.md"
    _ = write_text_under_book(book_name, book_id, batch_file_rel, batch_content)

    # 入库（一次）
    try:
        if book_id:
            sm = WriterStoreManager(book_id=book_id)
            await sm.store_volume_outline(book_detail)
    except RuntimeError as e:
        if "Called get_config outside of a runnable context" not in str(e):
            raise

    update = {"book_detail": book_detail, "outline_last_volume_number": max_required_volume}

    return update


__all__ = ["writer_outline_fn"]
