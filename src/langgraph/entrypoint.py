from __future__ import annotations

from typing import Any, Dict, Optional

from langgraph.config import get_config, get_stream_writer, RunnableConfig
from langgraph.func import entrypoint

from src.langgraph.nodes.book_setting.bs_planner_functional import bs_planner_fn
from src.langgraph.nodes.book_setting.bs_refine_functional import bs_refine_fn
from src.langgraph.nodes.book_setting.bs_steps_functional import bs_steps_fn
from src.langgraph.nodes.chapter.chapter_segment_functional import chapter_segment_fn
from src.langgraph.nodes.chapter.prepare_next_chapter_functional import prepare_next_chapter_fn
from src.langgraph.nodes.chapter.stream_write_functional import stream_write_fn
from src.langgraph.nodes.character.character_functional import writer_character_fn
from src.langgraph.nodes.outlines.outline_functional import writer_outline_fn
from src.langgraph.nodes.scene.scene_functional import writer_scene_fn
from src.langgraph.state import State


async def qflow_workflow_impl(inputs: dict, *, previous: Optional[State]) -> entrypoint.final[dict, State]:
    """
    QFlow 主流程核心实现（无装饰器）。
    由 build_qflow_workflow(checkpointer) 在运行期包裹为 Functional Entrypoint。
    """
    # 初始化/恢复 state
    state: Dict[str, Any] = dict(previous or {})
    if not state:
        msgs = None
        try:
            msgs = inputs.get("messages")
            if msgs is None and isinstance(inputs.get("llm_input"), dict):
                msgs = inputs.get("llm_input", {}).get("messages")
        except Exception:
            msgs = None
        if msgs:
            state["messages"] = msgs
        state.setdefault("resume_info", [])

    # 合并 configurable（inputs 优先），为各节点提供统一的配置来源
    incoming_cfg = {}
    try:
        incoming_cfg = inputs.get("configurable", {}) or {}
    except Exception:
        incoming_cfg = {}
    # 若 previous 中已有 configurable，做浅合并
    base_cfg = state.get("configurable") or {}
    try:
        merged_cfg = dict(base_cfg)
        merged_cfg.update(incoming_cfg)
    except Exception:
        merged_cfg = incoming_cfg or base_cfg or {}
    state["configurable"] = merged_cfg

    cfg = get_config()
    # 递归/步数限制（兜底 100）
    try:
        max_steps = int(getattr(cfg, "get", lambda *_: None)("recursion_limit") or 20)
    except Exception:
        max_steps = 20

    def step(s: Dict[str, Any]) -> str:
        # 简化决策：按阶段推进，节点函数只负责更新
        plan = s.get("writer_current_plan")
        if not plan:
            return "bs_planner"
        try:
            all_done = plan.is_all_step_completed()
        except Exception:
            all_done = False
        if not all_done:
            # 若存在需精炼项，交给 bs_refine；否则执行 bs_steps
            try:
                need_refine = any(getattr(st, "needs_refinement", lambda: False)() for st in (plan.plan_steps or []))
            except Exception:
                need_refine = False
            return "bs_refine" if need_refine else "bs_steps"

        # 角色
        if not s.get("character_summaries") or not getattr(s.get("character_summaries"), "characters", None):
            return "writer_character"
        if not s.get("character_details"):
            return "writer_character"
        try:
            if len(s["character_details"].characters) != len(s["character_summaries"].characters):
                return "writer_character"
        except Exception:
            return "writer_character"

        # 大纲
        bd = s.get("book_detail")
        if not bd or not getattr(bd, "volume_outlines", None):
            return "writer_outline"
        # 若入口指示生成下一卷
        if s.get("outline_generate_next_volume_number"):
            return "writer_outline"

        # 场景
        sd = s.get("scene_design")
        if not sd:
            return "writer_scene"

        # 写作/分章循环
        try:
            buffer_len = len((bd.stream_buffer or ""))
        except Exception:
            buffer_len = 0
        max_buffer = 8000
        loops = int(s.get("stream_write_self_loops") or 0)
        max_loops = 8
        if buffer_len >= max_buffer or loops >= max_loops:
            return "chapter_segment"

        # 若最近完成分章，准备下一章（由上游写入标记）
        # 简化：始终进入写作，由节点自身判断阈值后回写
        return "stream_write"

    # 主循环
    steps = 0
    # 若无 next_node，则从 step(state) 开始
    next_node = state.get("next_node") or step(state)
    while isinstance(next_node, str) and next_node not in ("__end__", "") and steps < max_steps:
        writer = get_stream_writer()
        writer(f"goto:{next_node}")
        # 直接调用对应节点（在各节点内由 @task 负责 checkpoint）
        if next_node == "bs_planner":
            update = await bs_planner_fn(state)
        elif next_node == "bs_steps":
            update = await bs_steps_fn(state)
        elif next_node == "bs_refine":
            update = await bs_refine_fn(state)
        elif next_node == "writer_character":
            update = await writer_character_fn(state)
        elif next_node == "writer_outline":
            update = await writer_outline_fn(state)
        elif next_node == "writer_scene":
            update = await writer_scene_fn(state)
        elif next_node == "stream_write":
            update = await stream_write_fn(state)
        elif next_node == "chapter_segment":
            update = await chapter_segment_fn(state)
        elif next_node == "prepare_next_chapter":
            update = await prepare_next_chapter_fn(state)
        else:
            update = {}
        writer(f"update:{update}")
        state.update(update or {})
        # 决策下一步
        next_node = step(state)
        state["next_node"] = next_node
        steps += 1

    return entrypoint.final(value={"next": next_node, "steps": steps}, save=state)


def build_qflow_workflow(checkpointer) -> Any:
    """在运行期用给定 checkpointer 构建可流式的 Functional Entrypoint。"""
    return entrypoint(checkpointer=checkpointer)(qflow_workflow_impl)


# 为 Dev Server / SDK 提供一个可直接导入的 Entrypoint（与 langgraph.json 对齐）
# 注意：不在模块层绑定数据库连接，避免异步上下文问题；服务端可注入 checkpointer。
qflow_workflow = entrypoint()(qflow_workflow_impl)


__all__ = ["build_qflow_workflow", "qflow_workflow_impl", "qflow_workflow"]
