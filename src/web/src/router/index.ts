import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  { path: '/', redirect: '/books' },
  { path: '/books', component: () => import('../views/Books.vue') },
  // 书籍详情：默认展示最新内容
  { path: '/books/:bookId', component: () => import('../views/Reader.vue') },
  // 历史版本查看：通过 query ?checkpoint=xxx 固定到历史
  { path: '/books/:bookId/checkpoint', component: () => import('../views/Reader.vue') },

]

const router = createRouter({ history: createWebHistory(), routes })
export default router

