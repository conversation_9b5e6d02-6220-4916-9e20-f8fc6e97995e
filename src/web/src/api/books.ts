import http from './axios'

export const listBooks = async () => (await http.get('/v1/books')).data
export const listCheckpoints = async (bookId: string, flat = false) =>
  (await http.get(`/v1/books/${bookId}/checkpoints`, { params: { flat } })).data
export const getReaderState = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/state/latest`, {
      params: { checkpoint_id: checkpointId },
    })
  ).data
export const getFullState = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/state/full`, {
      params: { checkpoint_id: checkpointId },
    })
  ).data
export const getPlan = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/plan`, { params: { checkpoint_id: checkpointId }})
  ).data
export const getSetting = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/setting`, { params: { checkpoint_id: checkpointId }})
  ).data
export const getOutlines = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/outlines`, { params: { checkpoint_id: checkpointId }})
  ).data
export const getCharacters = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/characters`, { params: { checkpoint_id: checkpointId }})
  ).data
export const getScenes = async (bookId: string, checkpointId?: string) =>
  (
    await http.get(`/v1/books/${bookId}/scenes`, { params: { checkpoint_id: checkpointId }})
  ).data
export const createBook = async (body: { book_name?: string; prompt?: string; write_chapters_cnt?: number; recursion_limit?: number }) =>
  (await http.post('/v1/books', body)).data
export const continueBook = async (
  bookId: string,
  body: { checkpoint_id?: string; prompt?: string; write_chapters_cnt?: number; recursion_limit?: number },
) => (await http.post(`/v1/books/${bookId}/continue`, body)).data

export const continueBookStream = (bookId: string, body: { checkpoint_id?: string; prompt?: string; write_chapters_cnt?: number; recursion_limit?: number }) =>
  new EventSource(`/api/v1/books/${bookId}/continue?payload=${encodeURIComponent(JSON.stringify(body))}`)

