import { defineStore } from 'pinia'
import { getReaderState, getPlan, getSetting, getOutlines, getCharacters, getScenes } from '../api/books'

export const useReaderStore = defineStore('reader', {
  state: () => ({
    data: null as any,
    loading: false,
    tabLoading: false,
    plan: null as any,
    setting: null as any,
    outlines: null as any,
    characters: null as any,
    scenes: null as any,
  }),
  actions: {
    async fetch(bookId: string, checkpointId?: string) {
      this.loading = true
      try {
        this.data = await getReaderState(bookId, checkpointId)
      } finally {
        this.loading = false
      }
    },
    async fetchPlan(bookId: string, checkpointId?: string) {
      this.tabLoading = true
      try {
        const res = await getPlan(bookId, checkpointId)
        this.plan = res?.plan ?? res
      } finally {
        this.tabLoading = false
      }
    },
    async fetchSetting(bookId: string, checkpointId?: string) {
      this.tabLoading = true
      try { this.setting = await getSetting(bookId, checkpointId) } finally { this.tabLoading = false }
    },
    async fetchOutlines(bookId: string, checkpointId?: string) {
      this.tabLoading = true
      try { this.outlines = await getOutlines(bookId, checkpointId) } finally { this.tabLoading = false }
    },
    async fetchCharacters(bookId: string, checkpointId?: string) {
      this.tabLoading = true
      try { this.characters = await getCharacters(bookId, checkpointId) } finally { this.tabLoading = false }
    },
    async fetchScenes(bookId: string, checkpointId?: string) {
      this.tabLoading = true
      try { this.scenes = await getScenes(bookId, checkpointId) } finally { this.tabLoading = false }
    },
    resetTabs() {
      this.plan = this.setting = this.outlines = this.characters = this.scenes = null
    }
  },
})

