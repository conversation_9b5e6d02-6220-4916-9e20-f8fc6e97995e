<template>
  <div>
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px">
      <h2>书籍</h2>
      <el-button type="primary" @click="showNew=true">新建书</el-button>
    </div>
    <el-table :data="booksStore.books" v-loading="booksStore.loading" style="width:100%">
      <el-table-column prop="book_name" label="书名" />
      <el-table-column prop="book_id" label="BookID" />
      <el-table-column prop="meta.checkpoint_count" label="Checkpoints" width="120" />
      <el-table-column prop="updated_ts" label="更新时间" width="220" />
      <el-table-column label="操作" width="140">
        <template #default="{row}">
          <el-button size="small" @click="$router.push(`/books/${row.book_id}`)">进入</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="showNew" title="新建书" width="480px">
      <el-form :model="newForm" label-width="120px">
        <el-form-item label="书名"><el-input v-model="newForm.book_name" /></el-form-item>
        <el-form-item label="用户要求"><el-input v-model="newForm.prompt" type="textarea" rows=4 /></el-form-item>
        <el-form-item label="章节数"><el-input-number v-model="newForm.write_chapters_cnt" :min="1" /></el-form-item>
        <el-form-item label="recursion_limit"><el-input-number v-model="newForm.recursion_limit" :min="1" /></el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showNew=false">取消</el-button>
        <el-button type="primary" :loading="creating" @click="onCreate">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { useBooksStore } from '../stores/books'
import { createBook } from '../api/books'
import { ElMessage } from 'element-plus'

const booksStore = useBooksStore()
const showNew = ref(false)
const creating = ref(false)
const newForm = reactive<{book_name: string; prompt?: string; write_chapters_cnt?: number; recursion_limit?: number}>({ book_name: '' })

onMounted(() => booksStore.fetchBooks())

async function onCreate() {
  creating.value = true
  try {
    const res = await createBook({ ...newForm })
    ElMessage.success('已创建，thread_id: ' + (res?.thread_id || res?.book_id))
    showNew.value = false
    await booksStore.fetchBooks()
  } finally {
    creating.value = false
  }
}
</script>

