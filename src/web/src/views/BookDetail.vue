<template>
  <div>
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px">
      <h2>书历史：{{ bookId }}</h2>
      <div>
        <el-button @click="$router.push(`/books/${bookId}`)">返回书籍详情</el-button>
      </div>
    </div>

    <el-card style="margin-bottom:16px">
      <div style="display:flex;gap:12px;align-items:center">
        <el-tag type="info">根节点：{{ tree?.roots?.length || 0 }}</el-tag>
        <el-tag type="success">节点：{{ tree ? Object.keys(tree.nodes || {}).length : 0 }}</el-tag>
      </div>
    </el-card>

    <el-row :gutter="16">
      <el-col :span="12">
        <el-card header="Checkpoint 时间线（新→旧）">
          <el-timeline>
            <el-timeline-item
              v-for="(item, idx) in flat"
              :key="item.checkpoint_id"
              :timestamp="item.ts"
            >
              <div style="display:flex;justify-content:space-between;align-items:center">
                <div>
                  <div style="font-weight:600">{{ item.checkpoint_id }}</div>
                  <div style="color:#888">thread: {{ item.thread_id }}</div>
                </div>
                <div>
                  <el-button size="small" @click="onRead(item.checkpoint_id)">查看</el-button>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card header="树形概览">
          <VueJsonPretty :data="tree" :showLine="false" :showDoubleQuotes="false" :deep="3" />
        </el-card>
      </el-col>
    </el-row>


  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import { useBookDetailStore } from '../stores/bookDetail'


const route = useRoute()
const router = useRouter()
const bookId = computed(() => route.params.bookId as string)
const store = useBookDetailStore()

const tree = computed(() => store.tree)
const flat = computed(() => store.flat)



onMounted(async () => {
  await Promise.all([store.fetchTree(bookId.value), store.fetchFlat(bookId.value)])
})

function onRead(checkpoint?: string) {
  // 跳到历史版本查看页；若不传 checkpoint 则查看最新
  if (checkpoint) router.push({ path: `/books/${bookId.value}/checkpoint`, query: { checkpoint } })
  else router.push({ path: `/books/${bookId.value}` })
}
</script>

