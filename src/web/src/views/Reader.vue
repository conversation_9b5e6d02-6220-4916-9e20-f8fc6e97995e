<template>
  <div>
    <!-- 顶部工具条：紧凑化 + 章节目录按钮 -->
    <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px">
      <div style="display:flex;gap:8px;align-items:center">
        <el-button size="small" @click="$router.back()">返回</el-button>
        <el-button size="small" @click="showChapters = true">章节目录</el-button>
      </div>
      <div>
        <el-select v-model="selectedCp" placeholder="切换checkpoint" size="small" @change="reload" style="min-width:220px">
          <el-option v-for="item in flat" :key="item.checkpoint_id" :label="item.checkpoint_id" :value="item.checkpoint_id" />
        </el-select>
      </div>
    </div>

    <el-empty v-if="!reader?.reader_projection" description="暂无内容" />

    <!-- 主内容去掉左侧固定章节栏，改为抽屉 -->
    <div v-else>
      <el-card>
        <el-tabs @tab-change="onTabChange" v-model="activeTab">
          <el-tab-pane label="Checkpoints" name="checkpoints">
            <el-skeleton :loading="detailStore.loading" animated>
              <template #default>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-card header="Checkpoint 时间线（新→旧）">
                      <el-timeline>
                        <el-timeline-item
                          v-for="(item, idx) in flat"
                          :key="item.checkpoint_id"
                          :timestamp="item.ts"
                        >
                          <div style="display:flex;justify-content:space-between;align-items:center">
                            <div>
                              <div style="font-weight:600">{{ item.checkpoint_id }}</div>
                              <div style="color:#888">thread: {{ item.thread_id }}</div>
                            </div>
                            <div>
                              <el-button size="small" @click="jumpToCheckpoint(item.checkpoint_id)">查看</el-button>
                            </div>
                          </div>
                        </el-timeline-item>
                      </el-timeline>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card header="树形概览">
                      <el-empty v-if="!treeNodes.length" description="暂无数据" />
                      <el-tree
                        v-else
                        :data="treeNodes"
                        node-key="id"
                        :props="{label:'label', children:'children'}"
                        @node-click="onTreeNodeClick"
                        highlight-current
                        default-expand-all
                        style="max-height: 70vh; overflow: auto;"
                      />
                    </el-card>
                  </el-col>
                </el-row>
              </template>
            </el-skeleton>
          </el-tab-pane>
          <el-tab-pane label="阅读" name="read">
            <div v-if="reader.reader_projection.chapters.length">
              <article v-for="ch in reader.reader_projection.chapters" :key="ch.number" :id="'ch-'+ch.number" class="chapter">
                <h2>第{{ ch.number }}章 {{ ch.title }}</h2>
                <pre class="content">{{ ch.content }}</pre>
              </article>
            </div>
            <div v-else>
              <template v-if="reader.reader_projection.stream_buffer && reader.reader_projection.stream_buffer.length">
                <h3>流式缓冲</h3>
                <pre class="content">{{ reader.reader_projection.stream_buffer }}</pre>
              </template>
              <el-empty v-else description="暂无内容" />
            </div>
          </el-tab-pane>

          <!-- 将“策划案 + 设定”合并为“计划”，左侧导航 + 右侧详情展示 -->
          <el-tab-pane label="计划" name="plan">
            <el-skeleton :loading="tabLoading" animated>
              <template #template>
                <el-skeleton-item variant="p" style="width: 60%" />
                <el-skeleton-item variant="p" style="width: 40%" />
              </template>
              <template #default>
                <el-empty v-if="!planSteps.length && !planThought && !settingObj.refined_book_setting" description="暂无计划信息" />
                <template v-else>
                  <el-row :gutter="16" style="height:calc(100vh - 170px);">
                    <!-- 左侧：思路 + 步骤（仅标题，可点击） -->
                    <el-col :span="6" :xs="24" style="height:100%;">
                      <div style="display:flex;flex-direction:column;height:100%;">
                        <el-card v-if="planThought" header="策划思路" style="margin-bottom: 12px">
                          <div class="content" v-html="md.render(planThought)" style="font-size: 13px; max-height: 80px; overflow-y: auto;" />
                          <div style="margin-top:8px; text-align:right">
                            <el-link type="primary" :underline="false" @click="showSetting">查看当前设定</el-link>
                          </div>
                        </el-card>

                        <el-card header="执行步骤" style="flex:1;display:flex;flex-direction:column;">
                          <div style="flex:1;overflow:auto;">
                            <template v-if="planSteps.length">
                              <div>
                                <div
                                  v-for="(s, i) in planSteps"
                                  :key="i"
                                  style="display:flex;align-items:center;justify-content:space-between;margin-bottom:8px;"
                                >
                                  <div
                                    class="step-title"
                                    @click="selectStep(i)"
                                    :style="stepStyle(s, i)"
                                  >
                                    {{ s.step_title || ('步骤 ' + (i+1)) }}
                                  </div>
                                </div>
                              </div>
                            </template>
                            <el-empty v-else description="暂无步骤" />
                          </div>
                        </el-card>
                      </div>
                    </el-col>

                    <!-- 右侧：详情区（默认当前设定，点击左侧后展示其他） -->
                    <el-col :span="18" :xs="24" style="height:100%;display:flex;flex-direction:column;">
                      <div style="flex:1;min-height:400px;overflow:auto;">
                        <template v-if="rightPanel.type">
                          <el-card v-if="rightPanel.type==='setting'" header="当前设定">
                            <el-descriptions :column="1" border size="small">
                              <el-descriptions-item label="书名">{{ reader?.raw_state?.book_name || '-' }}</el-descriptions-item>
                              <el-descriptions-item label="BookID">{{ reader?.meta?.book_id || '-' }}</el-descriptions-item>
                            </el-descriptions>
                            <div style="margin-top:12px">
                              <div 
                                v-if="settingObj.refined_book_setting" 
                                class="content" 
                                v-html="md.render(settingObj.refined_book_setting)"
                                style="line-height:1.5; color:#444;"
                              ></div>
                              <div v-else class="content" style="font-size:12px; line-height:1.5; color:#444;">（空）</div>
                            </div>
                          </el-card>
                          <el-card v-else-if="rightPanel.type==='step'" :header="currentStep?.step_title || ('步骤 ' + (rightPanel.stepIndex+1))">
                            <div style="display:flex;justify-content:flex-end;margin-bottom:8px">
                              <el-switch v-model="stepRaw" size="small" active-text="Raw" inactive-text="Markdown" />
                            </div>
                            <div style="margin-bottom: 16px; border-left: 3px solid #e0e0e0; padding-left: 12px; background: #fafbfc;">
                              <div style="font-weight: 600; margin-bottom: 2px; font-size: 13px; color: #888;">步骤说明</div>
                              <div v-if="currentStep?.step_description" class="content" v-html="md.render(String(currentStep.step_description))" style="font-size:12px; color:#666;"></div>
                              <div v-else class="content" style="color: #bbb; font-size:12px;">（无步骤说明）</div>
                            </div>
                            <div>
                              <div v-if="stepRaw">
                                <pre class="content">{{ currentStep?.step_execution_res || '（暂无）' }}</pre>
                              </div>
                              <div v-else class="content" v-html="md.render(String(currentStep?.step_execution_res || ''))"></div>
                            </div>
                          </el-card>
                        </template>
                        <el-empty v-else description="请选择左侧项" style="height:100%;display:flex;align-items:center;justify-content:center;" />
                      </div>
                    </el-col>
                  </el-row>
                </template>
              </template>
            </el-skeleton>
          </el-tab-pane>

          <el-tab-pane label="大纲/章节" name="outlines">
            <el-skeleton :loading="tabLoading" animated>
              <template #default>
                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-card header="卷大纲">
                      <el-empty v-if="!volumeOutlines.length" description="暂无卷大纲" />
                      <el-tree v-else :data="volumeOutlineTree" node-key="id" :props="{label:'label', children:'children'}" />
                    </el-card>
                  </el-col>
                  <el-col :span="16">
                    <el-card header="章节列表">
                      <div style="display:flex;justify-content:flex-end;margin-bottom:8px">
                        <el-button size="small" @click="showChapters = true">打开章节目录</el-button>
                      </div>
                      <el-empty v-if="!chaptersRows.length" description="暂无章节" />
                      <el-table v-else :data="chaptersRows" size="small" style="width:100%">
                        <el-table-column prop="chapter_number" label="章节" width="120" />
                        <el-table-column prop="chapter_title" label="标题" />
                        <el-table-column label="操作" width="120">
                          <template #default="{row}">
                            <el-button size="small" @click="previewChapter(row)">预览</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-card>
                  </el-col>
                </el-row>
                <el-dialog v-model="showPreview" title="章节预览" width="720px">
                  <h3>第{{ previewRow?.chapter_number }}章 {{ previewRow?.chapter_title }}</h3>
                  <pre class="content">{{ previewRow?.chapter_content }}</pre>
                </el-dialog>
              </template>
            </el-skeleton>
          </el-tab-pane>

          <el-tab-pane label="角色" name="characters">
            <el-skeleton :loading="tabLoading" animated>
              <template #default>
                <el-empty v-if="!characterCards.length" description="暂无角色" />
                <el-row v-else :gutter="12">
                  <el-col v-for="c in characterCards" :key="c.name" :span="8">
                    <el-card>
                      <div style="font-weight:600">{{ c.name }}</div>
                      <div style="color:#888; margin-top:4px">Tier: {{ c.tier || '-' }}</div>
                      <el-collapse style="margin-top:8px">
                        <el-collapse-item title="详情">
                          <pre class="content">{{ c.detailStr }}</pre>
                        </el-collapse-item>
                      </el-collapse>
                    </el-card>
                  </el-col>
                </el-row>
              </template>
            </el-skeleton>
          </el-tab-pane>

          <el-tab-pane label="场景" name="scenes">
            <el-skeleton :loading="tabLoading" animated>
              <template #default>
                <el-empty v-if="!sceneRows.length" description="暂无场景" />
                <el-table v-else :data="sceneRows" size="small">
                  <el-table-column prop="volume" label="卷" width="80" />
                  <el-table-column prop="name" label="场景" />
                  <el-table-column prop="estimated" label="预计章节" width="100" />
                  <el-table-column label="状态" width="120">
                    <template #default="{row}">
                      <el-tag :type="row.todo ? 'warning' : 'success'">{{ row.todo ? '待写' : '计划' }}</el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-skeleton>
          </el-tab-pane>

          <el-tab-pane label="续写" name="continue">
            <el-alert v-if="selectedCp" type="warning" :closable="false" title="当前查看历史版本，续写将基于该 checkpoint 进行分支。" style="margin-bottom:8px" />
            <el-form :model="contForm" label-width="120px" style="max-width:720px">
              <el-form-item label="checkpoint_id">
                <el-input v-model="contForm.checkpoint_id" placeholder="默认最新" />
              </el-form-item>
              <el-form-item label="用户要求">
                <el-input v-model="contForm.prompt" type="textarea" rows="4" />
              </el-form-item>
              <el-form-item label="recursion_limit">
                <el-input-number v-model="contForm.recursion_limit" :min="1" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" :loading="continuing" @click="doContinue">开始续写</el-button>
                <el-button v-if="es" :disabled="!continuing" @click="stopContinue">停止</el-button>
              </el-form-item>
            </el-form>

            <el-card v-if="streamLogs.length || streamBuffer" header="实时输出" style="margin-top:12px">
              <div style="display:flex; gap:12px;">
                <div style="flex:1; min-width: 320px;">
                  <div style="font-weight:600; margin-bottom:6px;">Token 流</div>
                  <pre class="content" ref="streamPre" style="max-height: 40vh; overflow: auto;">{{ streamBuffer }}</pre>
                </div>
                <div style="width: 360px;">
                  <div style="font-weight:600; margin-bottom:6px;">事件日志</div>
                  <el-scrollbar height="40vh">
                    <ul style="padding-left:16px;">
                      <li v-for="(l,i) in streamLogs" :key="i" style="font-size:12px; color:#666; margin-bottom:4px;">{{ l }}</li>
                    </ul>
                  </el-scrollbar>
                </div>
              </div>
            </el-card>
          </el-tab-pane>

          <el-tab-pane label="State" name="state">
            <el-skeleton :loading="tabLoading" animated>
              <template #default>
                <el-alert type="info" :closable="false" title="完整 state JSON（调试用）" style="margin-bottom:8px" />
                <VueJsonPretty :data="reader?.raw_state || {}" :showLine="false" :showDoubleQuotes="false" :deep="3" style="max-height:70vh;overflow:auto" />
              </template>
            </el-skeleton>
          </el-tab-pane>
        </el-tabs>
      </el-card>

      <!-- 章节抽屉 -->
      <el-drawer v-model="showChapters" title="章节目录" size="320px" :append-to-body="true">
        <el-scrollbar height="80vh">
          <el-menu>
            <el-menu-item
              v-for="ch in reader.reader_projection.chapters"
              :key="ch.number"
              @click="scrollTo(ch.number); showChapters=false"
            >
              第{{ ch.number }}章 {{ ch.title }}
            </el-menu-item>
          </el-menu>
        </el-scrollbar>
      </el-drawer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import MarkdownIt from 'markdown-it'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
import { useReaderStore } from '../stores/reader'
import { useBookDetailStore } from '../stores/bookDetail'
import { continueBookStream } from '../api/books'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const bookId = computed(() => route.params.bookId as string)
const selectedCp = ref<string | undefined>((route.query.checkpoint as string) || undefined)

const readerStore = useReaderStore()
const detailStore = useBookDetailStore()

const reader = computed(() => readerStore.data)
const flat = computed(() => detailStore.flat)

// 将树数据转为 el-tree 数据结构
const treeNodes = computed(() => {
  const tree = detailStore.tree
  if (!tree || !tree.nodes) return []
  const nodes = tree.nodes || {}
  const childrenMap = tree.children || {}
  function build(id: string): any {
    const n = nodes[id] || {}
    return { id, label: id, children: (childrenMap[id] || []).map(build) }
  }
  return (tree.roots || []).map(build)
})

function onTreeNodeClick(node: any) { jumpToCheckpoint(node?.id) }

const activeTab = ref('read')
const tabLoading = computed(() => readerStore.tabLoading)

// 章节抽屉状态
const showChapters = ref(false)

// 策划案
const rawPlan = computed(() => readerStore.plan?.plan || readerStore.plan || {})
const planThought = computed(() => rawPlan.value?.plan_thought || '')
const planSteps = computed(() => {
  const stepsRaw = Array.isArray(rawPlan.value?.plan_steps)
    ? rawPlan.value.plan_steps
    : (Array.isArray((rawPlan.value as any)?.steps) ? (rawPlan.value as any).steps : [])
  return stepsRaw.map((s: any) => ({
    step_title: s.step_title || s.title,
    step_description: s.step_description || s.desc,
    step_status: s.step_status || s.status,
    step_execution_res: s.step_execution_res,
  }))
})
// const activePlanIndex = computed(() => planSteps.value.findIndex((s: any) => s.step_status !== 'done') + 1)

// 右侧详情面板：默认空，由左侧点击触发展示（设定/步骤）
const rightPanel = reactive<{ type: '' | 'setting' | 'step'; stepIndex: number }>({ type: 'setting', stepIndex: -1 })
const currentStep = ref<any>(null)
const stepRaw = ref(false)
function showSetting() {
  rightPanel.type = 'setting'
  rightPanel.stepIndex = -1
}
function selectStep(i: number) {
  rightPanel.type = 'step'
  rightPanel.stepIndex = i
  currentStep.value = planSteps.value[i]
  stepRaw.value = false
}

// 使用 markdown-it 渲染
const md = new (MarkdownIt as any)({ html: false, linkify: true, breaks: true })

// 续写表单
const contForm = reactive<{ checkpoint_id?: string; prompt?: string; recursion_limit?: number }>({})
const continuing = ref(false)

// SSE 相关：流式 token 和事件日志
const es = ref<EventSource | null>(null)
const streamBuffer = ref('')
const streamLogs = ref<string[]>([])
const streamPre = ref<HTMLPreElement | null>(null)

function appendLog(s: string) {
  try {
    streamLogs.value.push(s)
    if (streamLogs.value.length > 500) streamLogs.value.shift()
  } catch {}
}

function stopContinue() {
  try { es.value?.close() } catch {}
  es.value = null
  continuing.value = false
}

function extractToken(val: any): string {
  if (typeof val === 'string') return val
  if (val && typeof val === 'object') {
    if (typeof (val as any).content === 'string') return (val as any).content
    try { return JSON.stringify(val) } catch { return String(val) }
  }
  return String(val ?? '')
}

async function doContinue() {
  continuing.value = true
  streamBuffer.value = ''
  streamLogs.value = []
  try {
    const body: any = { ...contForm }
    if (!body.checkpoint_id) body.checkpoint_id = selectedCp.value
    // 使用 SSE 流式展示：
    es.value = continueBookStream(bookId.value, body)
    ElMessage.info('开始续写…')

    es.value.onopen = () => appendLog('[open] 连接已建立')

    es.value.onmessage = (e: MessageEvent) => {
      try {
        const data = JSON.parse(e.data || '{}')
        const t = data?.type
        const v = data?.value
        if (t === 'message') {
          streamBuffer.value += extractToken(v)
          // 自动滚动到最底
          try { if (streamPre.value) streamPre.value.scrollTop = streamPre.value.scrollHeight } catch {}
        } else if (t === 'updates') {
          appendLog(`[updates] ${typeof v === 'object' ? Object.keys(v || {}).join(',') : String(v).slice(0,200)}`)
        } else if (t === 'values') {
          appendLog(`[values] ${typeof v === 'object' ? Object.keys(v || {}).join(',') : String(v).slice(0,200)}`)
        } else if (t === 'success') {
          appendLog('[success] 完成')
          ElMessage.success('完成')
          stopContinue()
          // 刷新最新状态
          readerStore.fetch(bookId.value, selectedCp.value)
        } else if (t === 'error') {
          appendLog(`[error] ${String(v)}`)
          ElMessage.error(String(v || '未知错误'))
          stopContinue()
        } else if (t === 'close') {
          appendLog('[close] 服务端关闭')
          stopContinue()
        } else {
          appendLog(`[${t || 'message'}] ${String(v).slice(0,200)}`)
        }
      } catch (err) {
        appendLog('[parse-error] 无法解析消息')
      }
    }

    es.value.onerror = () => { appendLog('[error] 连接异常'); stopContinue() }
  } catch (e) {
    continuing.value = false
  }
}

// 设定：增加回退到 raw_state，避免未加载分栏时为空
const settingObj = computed(() => {
  const fromApi = {
    rbs: readerStore.setting?.refined_book_setting,
  }
  const fromRaw = {
    rbs: reader.value?.raw_state?.refined_book_setting,
  }
  const prefer = (a: any, b: any) => {
    const as = typeof a === 'string' ? a.trim() : a
    return (as && !(typeof as === 'string' && as.length === 0)) ? a : b
  }
  return {
    refined_book_setting: prefer(fromApi.rbs, fromRaw.rbs),
  }
})

// 步骤样式函数：激活步骤高亮，但颜色规则也要按未激活的来
function stepStyle(s: any, i: number) {
  // 颜色规则
  let color = '#409EFF'
  if (!s.step_execution_res) {
    color = '#bfbfbf'
  } else if (s.step_status === 'refined') {
    color = '#67c23a'
  }

  // 激活步骤高亮，只加背景色，不改字号和padding
  if (rightPanel.type === 'step' && rightPanel.stepIndex === i) {
    return {
      color,
      cursor: 'pointer',
      background: '#f0f7ff'
    }
  }
  return {
    color,
    cursor: 'pointer'
  }
}

// 大纲/章节
const volumeOutlines = computed(() => readerStore.outlines?.volume_outlines || [])
const chaptersRows = computed(() => readerStore.outlines?.chapters || [])
const volumeOutlineTree = computed(() => {
  const arr = Array.isArray(volumeOutlines.value) ? volumeOutlines.value : []
  return arr.map((v: any, idx: number) => ({ id: idx+1, label: v.title || v.volume_title || `卷 ${idx+1}`, children: (v.scenes||[]).map((s: any, j: number) => ({ id: `${idx+1}-${j+1}`, label: s.name || s.title })) }))
})
const showPreview = ref(false)
const previewRow = ref<any>(null)
function previewChapter(row: any) { previewRow.value = row; showPreview.value = true }

// 角色
const characterCards = computed(() => {
  const sums = Array.isArray(readerStore.characters?.summaries) ? readerStore.characters.summaries : []
  const dets = readerStore.characters?.details || {}
  return sums.map((s: any) => ({
    name: s.name || s.character_name,
    tier: s.tier || s.importance,
    detailStr: JSON.stringify(dets[s.name] || dets[s.character_name] || {}, null, 2),
  }))
})

// 场景
const sceneRows = computed(() => {
  const sd = readerStore.scenes?.scene_design || []
  return (Array.isArray(sd) ? sd : []).map((x: any) => ({
    volume: x.volume || x.volume_index || '-',
    name: x.name || x.title,
    estimated: x.estimated_chapter || x.expected_chapter,
    todo: !!(x.todo || x.to_write),
  }))
})

onMounted(async () => {
  await detailStore.fetchFlat(bookId.value)
  await readerStore.fetch(bookId.value, selectedCp.value)
})

async function reload() {
  // 切换 checkpoint 时重置分栏数据
  readerStore.resetTabs()
  await readerStore.fetch(bookId.value, selectedCp.value)
  // 同步 URL：有 checkpoint → 固定历史；否则回到最新
  if (selectedCp.value) {
    router.replace({ path: `/books/${bookId.value}/checkpoint`, query: { checkpoint: selectedCp.value } })
  } else {
    router.replace({ path: `/books/${bookId.value}` })
  }
  // 若当前不在阅读标签，主动加载对应分栏数据
  await onTabChange(activeTab.value)
}

async function onTabChange(name: string) {
  if (name === 'read') return
  const cid = selectedCp.value as string | undefined
  const params = [bookId.value, cid] as [string, string | undefined]
  if (name === 'plan') {
    if (!readerStore.plan) await readerStore.fetchPlan(...params)
    if (!readerStore.setting) await readerStore.fetchSetting(...params)
    return
  }
  if (name === 'outlines' && !readerStore.outlines) return readerStore.fetchOutlines(...params)
  if (name === 'characters' && !readerStore.characters) return readerStore.fetchCharacters(...params)
  if (name === 'scenes' && !readerStore.scenes) return readerStore.fetchScenes(...params)
  if (name === 'checkpoints') {
    // 确保时间线/树数据加载
    if (!detailStore.flat.length) await detailStore.fetchFlat(bookId.value)
    if (!detailStore.tree) await detailStore.fetchTree(bookId.value)
    return
  }
}

// 监听标签切换
watch(activeTab, (n) => { if (n) onTabChange(n) })
// 监听路由 query 的变更（例如外部跳转到特定历史）
watch(() => route.query.checkpoint, async (v) => {
  const nv = (v as string) || undefined
  if (nv !== selectedCp.value) {
    selectedCp.value = nv
    await reload()
  }
})

function scrollTo(n: number) {
  const el = document.getElementById('ch-' + n)
  if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' })
}

function jumpToCheckpoint(checkpoint?: string) {
  if (checkpoint) router.push({ path: `/books/${bookId.value}/checkpoint`, query: { checkpoint } })
  else router.push({ path: `/books/${bookId.value}` })
}
</script>

<style>
/* Reader 全局加一些可读性样式 */
.el-card__body { padding: 12px; }
</style>

<style scoped>
.chapter { margin-bottom: 24px; }
.content { white-space: pre-wrap; line-height: 1.8; font-size: 16px; }
</style>
