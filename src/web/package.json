{"name": "qflowagent-web", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 5174"}, "dependencies": {"axios": "^1.7.2", "element-plus": "^2.8.0", "markdown-it": "^14.1.0", "pinia": "^2.1.7", "vue": "^3.4.31", "vue-json-pretty": "^2.5.0", "vue-router": "^4.4.0"}, "devDependencies": {"@types/node": "^20.11.30", "@vitejs/plugin-vue": "^5.0.4", "typescript": "^5.4.2", "vite": "^5.3.1"}}