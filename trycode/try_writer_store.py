#!/usr/bin/env python3
"""
Writer存储功能测试脚本
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))

from src.init_log import init_log
from src.langgraph.nodes.common.book_types import WriterPlan, WriterPlanStep
from src.langgraph.nodes.common.store_manager import WriterStoreManager
from src.memory.store_utils import get_store_and_checkpointer

# 初始化日志
init_log()
logger = logging.getLogger(__name__)


async def test_writer_store():
    """测试writer存储功能"""

    # 创建测试数据
    test_plan = WriterPlan(
        book_name="测试小说",
        book_description="这是一个测试小说的简介",
        plan_thought="测试策划思路",
        plan_steps=[
            WriterPlanStep(
                step_title="世界观设定",
                step_description="构建小说的世界观，包括基础世界观架构和力量体系",
                step_execution_res="这是世界观设定的完整内容，包括基础世界观和力量体系...",
            ),
            WriterPlanStep(
                step_title="角色设定",
                step_description="设定主要角色，包括主角的背景和能力",
                step_execution_res="这是主角的详细设定...",
            ),
        ],
    )

    book_id = "test_book_id"

    try:
        async with get_store_and_checkpointer() as (store, checkpointer):
            print("🔄 开始测试存储功能...")
            store_manager = WriterStoreManager(book_id=book_id, store=store)

            # 1. 测试存储策划案
            print("\n1. 测试存储策划案")
            version = await store_manager.store_writer_plan(test_plan)
            print(f"✅ 策划案已存储，版本: {version}")

            # 2. 测试获取策划案
            print("\n2. 测试获取策划案")
            retrieved_plan = await store_manager.get_writer_plan()
            if retrieved_plan:
                print(f"✅ 成功获取策划案: {retrieved_plan.book_name}")
                print(f"   步骤数量: {len(retrieved_plan.plan_steps)}")
            else:
                print("❌ 获取策划案失败")

            # 3. 测试存储步骤结果
            print("\n3. 测试存储步骤结果")
            for step in test_plan.plan_steps:
                if step.step_execution_res:
                    doc_ids = await store_manager.store_step_result(
                        step=step,
                        result_content=step.step_execution_res,
                    )
                    print(f"✅ 步骤结果已存储: {step.step_title}")
                    print(f"   文档ID: {doc_ids}")

            # 4. 测试搜索设定
            print("\n4. 测试搜索设定")
            search_queries = ["世界观", "主角", "力量体系"]
            for query in search_queries:
                results = await store_manager.search_settings(query=query, limit=3)
                print(f"🔍 搜索 '{query}' 找到 {len(results)} 个结果")
                for i, result in enumerate(results):
                    metadata = result.value.get("metadata", {})
                    print(f"   结果 {i+1}: {metadata.get('step_title')}")

            # 5. 测试获取特定步骤结果
            print("\n5. 测试获取特定步骤结果")
            step_result = await store_manager.get_step_result(step_title="世界观设定")
            if step_result:
                print(f"✅ 成功获取步骤结果，长度: {len(step_result)}")
            else:
                print("❌ 获取步骤结果失败")

            # 6. 测试存储章节
            print("\n6. 测试存储章节")
            chapter_content = """
第一章 开始

这是第一章的内容，讲述了主角的故事开始...

在这个充满魔法的世界里，主角踏上了冒险的旅程。
他拥有着特殊的能力，但还不知道如何使用。

随着故事的发展，主角将逐渐发现自己的真正力量...
"""
            doc_ids = await store_manager.store_chapter(
                chapter_title="开始",
                chapter_content=chapter_content,
                chapter_number=1,
            )
            print(f"✅ 章节已存储，文档ID: {doc_ids}")

            print("\n🎉 所有测试完成！")

    except Exception as e:
        logger.error(f"测试过程中出错: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_writer_store())
