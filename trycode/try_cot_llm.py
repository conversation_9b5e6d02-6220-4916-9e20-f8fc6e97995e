import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent.parent))
import json
import logging
import os
from pathlib import Path
from typing import Any, Dict, <PERSON><PERSON><PERSON>, Literal, Tuple

from langchain.chat_models import init_chat_model
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.language_models import BaseChatModel
from langchain_openai import ChatOpenAI, OpenAIEmbeddings
from src.common.models.model_list import get_llm_params
from src.init_log import init_log
from src.langgraph.nodes.common.book_types import WriterPlan, WriterPlanStep
from src.langgraph.nodes.common.store_manager import WriterStoreManager
from src.memory.store_utils import get_store_and_checkpointer
from tqdm import tqdm

cot_llm_list = [
    "deepseek-reasoner",
    "qwen-turbo-latest:thinking",
]


async def test_deepseek_via_openai_provider():
    llm_params = get_llm_params("deepseek-reasoner")
    llm = init_chat_model(**llm_params)

    chunks = llm.astream("How many Rs are there in the word 'strawberry'?")
    # parse response
    first = True
    async for chunk in chunks:
        if first:
            response = chunk
            first = False
        else:
            response = response + chunk
        print(response)

    print(response)


async def test_deepseek_via_chatdeepseek():
    from langchain_deepseek import ChatDeepSeek

    llm_params = get_llm_params("deepseek-reasoner")
    llm_params.pop("model_provider")
    llm = ChatDeepSeek(**llm_params)

    chunks = llm.astream("如何写刘慈欣风格的玄幻小说，请你给我的AI助手写一份优质的通用提示词")

    # parse response
    first = True
    async for chunk in chunks:
        if first:
            response = chunk
            first = False
        else:
            response = response + chunk

        print(chunk.additional_kwargs.get("reasoning_content", ""), end="", flush=True)
        print(chunk.content, end="", flush=True)

    print(f"{response.additional_kwargs.get("reasoning_content")}", end="", flush=True)
    print(f"{response.content}", end="", flush=True)


if __name__ == "__main__":
    asyncio.run(test_deepseek_via_chatdeepseek())
