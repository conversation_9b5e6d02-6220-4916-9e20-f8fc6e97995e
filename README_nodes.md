# QFlowAgent 节点架构说明（v2 已对齐）

本说明文档面向开发者，系统性讲解 `src/nodes` 的基类、专用基类与业务节点的设计与协作方式，并给出数据流、人工审核与持久化的整体视图，便于快速扩展新节点与定位问题。

---

## 架构总览（已迁移）

- 函数式节点：每个节点为 `async def node(state, config) -> dict`（只返回更新，不再返回 Command/goto）
- 工具层 `NodeKit`：`llm_text/llm_json`、`write_text_under_book()/write_batch_under_book()`、`build_resume_info()/build_stream_resume_info()`、`rel_path_for_chapter()`、`save_plan_to_db()/save_chapter_to_db()`
- 并发：对可并行阶段采用 foreach + join（角色/大纲/场景/流式后处理等）

### 类关系图（Mermaid）

### 执行序列（函数式节点）

```mermaid
sequenceDiagram
  participant Runner as Graph/Runner
  participant Node as function
  participant LLM as LLM
  participant Store as File/DB

  Runner->>Node: node(state, config)  Note over Runner,Node: Functional API（入口集中控制流）
  Node->>LLM: NodeKit.llm_json/llm_text
  LLM-->>Node: parsed/content
  Node->>Store: NodeKit.write_text_under_book()/save_*_to_db()
  Node-->>Runner: dict(update)
```

---

## 新范式：函数式节点 + 工具层（NodeKit）

- 函数式节点：每个节点为 `async def node(state, config) -> dict`，仅关注业务最小逻辑与状态更新（不包含流程跳转）。
- 工具层 NodeKit：统一提供 `llm_text/llm_json`、`write_text_under_book`、`build_resume_info/build_stream_resume_info`。
- 并发：通过 LangGraph tasks（spawn/join）实现 foreach 并发与聚合。
- CLI 友好名：事件输出对节点/任务采用友好名称映射，并打印耗时。

## CLI 完成行与进度预览（最小片段）

- 完成行优先展示最近一次 `resume_info` 的摘要：
  - **任务进度**：`[completed/total]`
  - **流式缓冲**：`[buffer=..., chunks=...]`

示例（完成行）：

```
✓ 大纲细化-汇总  [3/5] (1234 ms)
✓ 流式后处理-汇总  [buffer=8123, chunks=4]
```

（简化）当前不展示“在飞队列预览/重派发统计”，仅打印关键完成摘要与 Debug 单行日志。

### LLM 文本稳健性配置与示例（llm_text）

- 配置键（`config.configurable`）：
  - `llm_text_min_length`：文本最小长度（整数，默认 0 关闭）
  - `llm_text_sensitive_patterns`：敏感模式正则（字符串或数组），命中触发一次回退重试
  - `llm_text_retry_tier`：回退重试时升级的模型等级（`low|medium|high`）

- 示例（apps 默认已开启）：

```python
from src.langgraph.nodes.common.node_kit import llm_text

content, cot = await llm_text(
    "writer_outline",
    messages,
    config={
        "configurable": {
            "llm_text_min_length": 200,
            "llm_text_sensitive_patterns": [r"(?i)\\b(placeholder|todo)\\b"],
            "llm_text_retry_tier": "high",
        }
    },
)
```

- 行为：若首次响应过短或命中敏感正则，将自动追加一条 review 提示并重试一次（可升级 tier）；二次仍不通过则抛错，上层通常进入人审或分支重试。

---

## 业务节点

（预留）dynamic_character（函数式，规划中）：`src/nodes/character/dynamic_character_functional.py::dynamic_character_fn`
  - 基于场景设计分析角色缺口与优先级，输出到 `character/第N卷角色需求分析.md`，并在状态中标记 `pending_character_creation`。

- stream_write（函数式）：以“大纲+场景+角色”上下文持续写入 `BookDetail.stream_buffer`；缓冲超阈时按 `stream_post_chunk_size` 切块并发执行 `stream_post_task`，聚合在节点内部完成，然后进入 `chapter_segment`（v2/v3 起已移除人工审核闸门）。
  - 相关文件：`src/nodes/chapter/stream_write_functional.py`、`src/nodes/chapter/stream_write_tasks.py`
  - ResumeInfo：推荐使用 `NodeKit.build_stream_resume_info(...)` 构造摘要（包含 `buffer_len` 与可选 `chunks_count`）。

- chapter_segment（函数式）：`src/nodes/chapter/chapter_segment_functional.py::chapter_segment_fn`
  - 从缓冲切出 3000~5000 字一章，落盘 `chapter/第N章-标题.md`，扣除缓冲并循环；达目标章节数则结束。

- prepare_next_chapter（函数式）：`src/nodes/chapter/prepare_next_chapter_functional.py::prepare_next_chapter_fn`
  - 每章后汇总 L1/L2/L3 摘要，评估是否切换场景/补足当前卷或推进下一卷；根据结果回到 `stream_write` 或转至 `writer_scene`，完成条件下到 `__end__`。

人工审核与 interrupt 流程已移除；保留占位 API 以兼容旧调用但统一直通。

---

## 函数式并发节点范式（示例，Functional API）

以下示例展示如何在函数式节点中并发处理一批子任务（使用 @task 子任务 + await 聚合），不再返回 Command/goto：

```python
from langgraph.func import task

@task
async def refine_one_task(payload: dict) -> dict:
    idx = int(payload.get("task_target_index"))
    # ... 执行单个目标的 LLM 请求与结果构造
    return {"index": idx, "ok": True}

@task
async def spawn_and_join(state: dict, indices: list[int]) -> dict:
    futures = [refine_one_task({"task_target_index": i}) for i in indices]
    results = []
    for f in futures:
        try:
            results.append(f.result())
        except Exception:
            results.append(await f)
    # 聚合为一次性更新并返回
    return {"batch_results": results}
```

最佳实践：
- 幂等：`join_node` 设计为可重入，未齐全时返回 `goto="join_node"` 等待。
- 命名：使用 `result_<id>`/`expected` 约定，便于遍历与对齐。
- 批量文件：聚合后统一落盘。
- 范围信息：在 `state` 写入批次范围（如当前卷与最大卷），用于文件命名。

并发命名建议：
- 任务节点：`<domain>_refine_one_task`（如 `writer_outline_refine_one`）
- 聚合节点：`<domain>_join_node`（如 `writer_outline_join_node`）
- 任务派发：使用 `Send("<task_node>", {"task_target_index": i})`，并将期望集合写入 `state.outline_task_expected_indices` 等约定字段

已迁移：大纲/角色/场景已采用 LangGraph tasks（spawn/join），具备任务级可视化与更友好的恢复能力。

---

## ResumeInfo 摘要模板（统一风格）

每个函数式节点在成功执行后调用 `NodeKit.build_resume_info()/build_stream_resume_info()` 追加一条摘要，便于 `checkpoint` 列表与树形视图展示关键信息。该函数支持附加结构化元信息（如 `step_index`、`step_title`、`target_volume`、`buffer_len` 等）。建议模板：

- bs_planner：`策划案创建《{book_name}》 -> {next}`
- bs_steps：`设定执行：第{step_index}/{total_steps}步 {step_title} -> {next}`
- bs_refine：`设定精炼：第{step_index}/{total_steps}步 {step_title} -> {next}`
- writer_character：`角色：{count} 角色已完成/共 {total} -> {next}`
- dynamic_character：`角色需求分析：第{volume}卷 -> {next}`
- writer_outline（并发聚合）：`大纲：tasks 并发细化 第{start_vol}~{end_vol}卷 -> {next}`
- writer_scene：`场景：第{start_vol}~{end_vol}卷设计/更新 -> {next}`
- stream_write：`流式写作：缓冲{buffer_len}字 -> {next}`
- chapter_segment：`智能分章：生成第{chapter_number}章，缓冲{buffer_len}字 -> {next}`

注意：
- 仅在成功更新状态且决定了 `next_node` 后追加摘要
- 保持字段：`node`、`next`、`summary`，必要时补充 `stage`/`step_title`/`target_volume`/`buffer_len` 等键，便于 CLI 展示

---

## 并发与 CLI 示例

- 目标：将现有 `asyncio.gather` 并行改为 LangGraph task（spawn/join）。
- 迁移收益：
  - 任务级可视化（on_task_start/on_task_end）
  - Checkpointer 更友好（子任务上下文可被安全恢复）
  - CLI 可统一展示任务生命周期。
- CLI：`apps/cli_events.py` 已预留 `on_task_*` 事件处理与打印；迁移后可直接受益。
  - 已启用：`writer_outline_*`、`writer_scene_*`、`writer_character_*`、`writer_stream_post_*`

示意（伪代码）：

```python
# 子任务函数
async def refine_volume_task(i: int):
    ...
    return i, refined

# 迁移前：asyncio.gather
# 已由 tasks 替代：使用 Send + join 节点的聚合

# LangGraph task（spawn -> join）
# for i in indices:
#     task_id = task.spawn(refine_volume_task, args=(i,))
# results = task.join()
```

---

## 审核（已移除）

- v2/v3 起移除人工审核与 interrupt 流程；`NodeKit.maybe_interrupt_review(...)` 为占位直通实现，仅用于兼容调用与旧测试。

## 运行入口（Functional API）

`src/functional/entrypoint.py` 提供 `qflow_workflow` 作为统一入口；恢复/回退由 apps/main.py 与 Postgres Checkpointer 直接处理。事件通过 `.stream(astream)` 输出 `updates/debug`。

```mermaid
flowchart LR
  bs_planner --> bs_steps
  bs_steps --> bs_refine
  bs_refine --> writer_character
  writer_character --> writer_outline
  writer_outline --> writer_scene
  writer_scene --> stream_write

  bs_steps -- "条件" --> bs_steps
  bs_steps -- "条件" --> bs_refine

  bs_refine --> bs_steps
  bs_refine --> writer_character

  writer_character --> writer_character
  writer_character --> writer_outline

  writer_outline --> writer_outline
  writer_outline --> writer_scene

  writer_scene --> writer_scene
  writer_scene --> stream_write

  stream_write --> stream_write
  stream_write --> chapter_segment

  chapter_segment --> prepare_next_chapter
  prepare_next_chapter --> stream_write
  prepare_next_chapter --> writer_scene
  prepare_next_chapter --> end((__end__))
```

---

## 状态与数据模型

- State（`src/state.py`）关键字段：
  - `book_id`、`book_name`、`messages`、`user_original_request`
  - `writer_current_plan: WriterPlan`
  - `refined_book_setting`、`refined_book_outlines`
  - `book_detail: BookDetail`（含 `volume_outlines`、`chapters`、`stream_buffer`）
  - `character_summaries: CharacterSummaryCollection`、`character_details: CharacterDetailCollection`
  - `scene_design: BookSceneDesign`（按卷的 `VolumeSceneDesign`）
  - `resume_info: list[dict]`（每个函数式节点在成功执行后通过 `NodeKit.build_resume_info()/build_stream_resume_info()` 追加摘要，便于回放/恢复）
  - `skip_counters: dict[str, int]`（跨轮 `/skip <domain> [n]` 策略剩余次数）
  - 流式后处理 join 观测/限流：
    - `stream_post_pending_indices`、`stream_post_dispatched_indices`
    - `stream_post_dispatch_times`、`stream_post_requeue_counts`
    - `stream_post_inflight_count`、`stream_post_inflight_preview`
  - 章节入库 join 观测/限流：
    - `chapter_store_pending_numbers`、`chapter_store_dispatched_numbers`
    - `chapter_store_dispatch_times`、`chapter_store_requeue_counts`
    - `chapter_store_inflight_count`、`chapter_store_inflight_preview`
  - 提供 `serialize_state()/deserialize_state()`，支持 LZMA + Base64 或原始 JSON。

- 模型（`src/nodes/common/book_types.py`）：
  - 计划：`WriterPlanSchema` → `WriterPlan`、`WriterPlanStep(StepStatus)`
  - 大纲：`VolumeOutline`、`ChapterOutline`、`BookDetail`
  - 角色：`CharacterSummary/Detail` 及其集合、生命周期与分层
  - 场景：`Scene`、`VolumeSceneDesign`、`BookSceneDesign`
  - 审核：`HumanReviewParams`

---

## 持久化与检索

`WriterStoreManager`（`src/nodes/common/store_manager.py`）：
  - 命名空间（v2）：`("writer_app_v2", book_id, data_type)`；支持 `plan/settings/chapters/outline/scene/stream`。
  - 功能：
    - `store_writer_plan()`、`store_step_result()`（含分块+向量索引）、`store_refined_book_setting()`
    - `store_volume_outline()`、`store_scene_design()`（并对场景文本做分块向量化）
    - 新增：`store_stream_segment(batch_id, chunk_index, content)`（流式后处理分块入库，索引字段 `content`）
    - `search_settings(query, limit)`：设定检索（语义搜索）

---

## 幂等键与存储重试（v2 数据层）

- 命名空间/键约定（幂等覆盖）：
  - 章节全文：`("writer_app_v2", book_id, "chapters")` + `chapter_{N}_{title}_full`
  - 章节分块：`chapter_{N}_{title}_chunk_{i}`（与全文共享基础键前缀）
  - 流式分块：`("writer_app_v2", book_id, "stream")` + `batch_{batch_id}_chunk_{i:03d}`
  - 设定/步骤/大纲/场景：均采用 `*_full` 与 `{key}_chunk_{i}` 组合

- 写入策略：固定键写入即幂等，不产生重复；分块写入统一 `index=["content"]` 以支持向量检索。

- 失败重试：关键写入统一启用非破坏性重试与退避（`store_aput(..., retries=2, retry_backoff_s=0.5)`）。调用方无需关心，失败由上层捕获处理。

- 章节入库已简化为单节点 `writer_chapter_store_node`（不再维护 join/重派发队列）。流式后处理 join 已合并回 `stream_write` 节点内部。

---


```mermaid
sequenceDiagram
  participant Node as 函数式节点
  participant CLI as CLI 输入
  participant HR as interrupt(human_review)
  participant FS as File/DB

  Node->>HR: interrupt(payload: 文件/内容/域)
  CLI-->>HR: /pass 或 /skip 或 (/high|/medium|/low + 意见) 或 文本
  alt /pass 或 /skip
   HR-->>Node: 清理审核状态，进入 next_node
  else 文本/等级
    HR->>Node: 反馈意见（messages 追加 review）
    Node->>FS: 重生并覆盖文件
    Node-->>HR: 保持在当前节点，继续审核循环
  end
```

---

## 文件输出规范（相对工作区路径）

- `book_name/book_id/setting/`：策划案与精炼产物
- `book_name/book_id/character/`：角色简介与详情
- `book_name/book_id/outline/`：分卷大纲与细化大纲
- `book_name/book_id/scene/`：场景清单与分析报告
- `book_name/book_id/chapters_stream/stream_buffer.md`：流式写作缓冲
- `book_name/book_id/chapter/第N章-标题.md`：分章成品

---

## 配置项要点（`config.configurable`）

- `thread_id`：作为 `book_id` 使用
- `prompt_budget_tokens`：提示词预算
- `model_tier`、`llm_params`：模型等级/参数覆盖
- `book_name`、`demo_chapter`、`demo_bs`：演示/上下文素材
- `enable_interrupt_review`：启用基于 LangGraph interrupt 的人工审核
- `enable_smart_character_management`：启用智能角色需求分析
- `segment_min_len`：写作目标与分章阈值

---

## 新增节点实践指南

1) 范式：统一“函数式节点 + NodeKit”
- 文本生成：`NodeKit.llm_text()`；结构化生成：`NodeKit.llm_json()`
- 控制流：集中在 entrypoint 的 step()，节点仅返回 dict 更新

2) 最小实现清单（函数式）
- 构造上下文：使用 `ContextBuilder`（必要时附 `json_schema`/检索片段）
- 调用 LLM：`NodeKit.llm_text/llm_json`
- 落盘/入库：`NodeKit.write_text_under_book()/save_*_to_db()`
- 摘要：`NodeKit.build_resume_info()/build_stream_resume_info()` 统一写入 `resume_info`
- 跳转：不再由节点决定，改为入口 step() 决策

3) 与图衔接
- 在函数式节点中不再使用 `Command`；主循环由 `qflow_workflow` 的 step() 推进

---

## 迁移进展（函数式节点 + NodeKit）

- 已将以下节点接入“函数式实现 + NodeKit”并在主图启用：
  - `bs_planner` → `src/nodes/book_setting/bs_planner_functional.py::bs_planner_fn`
  - `bs_steps` → `src/nodes/book_setting/bs_steps_functional.py::bs_steps_fn`
  - `stream_write` → `src/nodes/chapter/stream_write_functional.py::stream_write_fn`

注：本节仅为迁移过渡说明，完整方案见 `README_plan.md`。

---

如需进一步阅读，请从以下文件入手：

- （历史）面向对象基类（BaseNode 家族）已清退，不再提供代码文件；统一采用函数式节点 + `NodeKit`。
- `src/functional/entrypoint.py`（Functional 入口）
- `src/state.py`（状态结构与序列化）
- `src/nodes/common/book_types.py`（领域模型）
- `src/nodes/common/store_manager.py`（持久化与检索）


