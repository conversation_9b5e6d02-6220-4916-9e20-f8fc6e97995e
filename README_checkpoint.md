## Checkpoint 管理与恢复

### 概念

- book_id 与 thread_id：首次创建项目时二者相同；同一书籍后续可能产生多个 `thread_id` 分支，但其 checkpoint 的 `values/metadata` 均带相同 `book_id`。
- LangGraph 组件：
  - AsyncPostgresSaver（checkpointer）：保存/遍历 checkpoint（按 `thread_id`）。
  - AsyncPostgresStore（store）：保存业务数据与向量索引（按命名空间）。
- 业务命名空间：`("writer_app_v2", <book_id>, "plan"|"settings"|"outline"|"scene"|"chapters")`
 

### 数据模型

- BookGroupSummary：{book_id, book_name?, created_ts?, updated_ts?, representative_thread_id, thread_ids}
- CheckpointSummary：{checkpoint_id, thread_id, book_id, ts?, metadata, parent_checkpoint_id?}

### 接口

- connect() -> AsyncIterator[CheckpointManager]
  - 返回绑定了 `store` 与 `checkpointer` 的管理器（来源于 `get_store_and_checkpointer()`）。
- list_books() -> List[BookGroupSummary]
  - 遍历 `checkpointer.alist({})` 聚合到 `book_id` 维度；必要时通过 `WriterStoreManager.get_writer_plan()` 补齐 `book_name`。
- list_checkpoints(book_id) -> List[CheckpointSummary]
  - 过滤出匹配 `book_id` 的 checkpoint；展示信息优先取 `values['resume_info'][-1]`。
- build_checkpoint_tree(book_id) -> (roots, nodes, children)
  - nodes: {cid: {id, parent, ts, thread_id, metadata}}；children: {parent_id: [child_id, ...]}。
- delete_book(book_id, workspace_path=Path("./workspace"), dry_run: bool = False) -> None
  - 清理顺序：
    1) 汇总该书的 `thread_ids` 并对每个 `thread_id` 调用 `checkpointer.adelete_thread`。
    2) `store.asearch(("writer_app_v2", book_id), ...)` 枚举所有项，逐条 `store.adelete(item.namespace, item.key)`。
    3) 清理工作区：删除 `workspace/<book_name>/<book_id>/` 目录。
 - get_latest_checkpoint(book_id) -> Optional[CheckpointSummary]
   - 返回指定书籍的最新 checkpoint。
 - get_latest_checkpoint_global() -> Optional[CheckpointSummary]
   - 返回全局最新 checkpoint（与书籍无关）。

### 与 LangGraph 的交互

- `checkpointer.alist({})` 返回的记录包含 `thread_id`、`checkpoint` 等字段；`checkpoint.values/state/metadata` 中提取 `book_id`、`ts`、`id`。
- `store.asearch(namespace_prefix, ...)` 返回 `SearchItem`：包含 `namespace` 与 `key`，删除时需调用 `store.adelete(item.namespace, item.key)`。
- 连接与资源来自 `src/memory/store_utils.get_store_and_checkpointer()`，其中定义了 DB URI 与向量索引配置。

### 使用示例

```python
from pathlib import Path
from src.langgraph.checkpoint import CheckpointManager

async with CheckpointManager.connect() as mgr:
    # 全局最新 checkpoint（用于 `--checkpoint-id latest`）
    latest = await mgr.get_latest_checkpoint_global()
    if latest:
        thread_id = latest.thread_id
        checkpoint_id = latest.checkpoint_id
    # 删除一本书（不可恢复）
    # 先预览
    await mgr.delete_book(book_id="...", workspace_path=Path("./workspace"), dry_run=True)
    # 再删除
    await mgr.delete_book(book_id="...", workspace_path=Path("./workspace"), dry_run=False)
```

### 交互输出

- format_book_line(index, book): 单行书籍信息
- format_checkpoint_line(index, checkpoint): 单行 checkpoint 信息（含摘要与关键信息），用于回退时的提示展示。

### 建议

- 分页删除：当前采用“循环 asearch 直到空”的方式；如底层实现变更，建议改为显式 offset 递增的分页方案。
- 观察日志：对 `adelete_thread`、`adelete` 失败场景保持 warning 级别日志，便于定位残留。
- 性能优化：可增加针对 `book_id` 的预索引/过滤方案，减少全量遍历；或在 `list_books()` 中优先读取 `plan` 提速。
- 职责边界：保持 UI 交互与数据层解耦；非必要不在管理器中引入输入/输出逻辑。

### 测试

```bash
uv run pytest -q
```
