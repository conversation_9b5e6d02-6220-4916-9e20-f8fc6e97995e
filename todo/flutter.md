### 第二部分：Flutter 客户端方案

这份文档详细阐述了 AI 小说写作 App 的客户端架构设计，选择 Flutter 作为开发框架，旨在实现跨平台、高性能且用户体验一致的应用程序。

# AI小说写作App技术方案 (Flutter 客户端)

> 适配说明：本节基于当前仓库现状（LangGraph 内核 + 计划引入 FastAPI）进行了接口与数据契约的贴合。Flutter 作为客户端将通过 HTTP 与 `/src/fastapi` 提供的 `/api/v1` 接口交互。暂不直接依赖 Python 运行时或数据库。


## 1. 客户端架构设计

客户端是用户与AI小说生成系统交互的主要界面，需要提供直观、流畅的用户体验。我们选择Flutter作为开发框架，以实现跨平台部署并保持一致的用户体验。Flutter的单一代码库特性将大大提高开发效率，同时其优秀的性能和丰富的UI组件库能够满足复杂的用户界面需求。

### 1.1 技术选型

*   **开发框架**: Flutter 3.x
*   **编程语言**: Dart
*   **状态管理**: Riverpod (推荐) 或 Bloc
*   **网络请求**: Dio
*   **本地存储**: Hive 或 SharedPreferences
*   **路由管理**: GoRouter
*   **UI组件库**: Material Design 3 + 自定义组件
*   **依赖注入**: GetIt + Injectable

### 1.2 应用架构模式

我们将采用Clean Architecture（清洁架构）模式，结合MVVM（Model-View-ViewModel）模式，确保代码的可测试性、可维护性和可扩展性。

```
book_app/
├── lib/
│   ├── main.dart
│   ├── app/
│   │   ├── app.dart                    # 应用入口
│   │   ├── router.dart                 # 路由配置
│   │   └── theme.dart                  # 主题配置
│   ├── core/
│   │   ├── constants/
│   │   ├── errors/
│   │   ├── network/
│   │   ├── storage/
│   │   └── utils/
│   ├── features/
│   │   └── book/
│   │       ├── data/
│   │       ├── domain/
│   │       └── presentation/
│   ├── shared/
│   │   ├── widgets/
│   │   └── extensions/
│   └── injection_container.dart         # 依赖注入配置
├── test/
├── assets/
├── pubspec.yaml
└── analysis_options.yaml
```
