# QFlowAgent 使用与命令行（Commands）

本文件汇总安装、配置、运行与命令行相关内容。架构设计请见根目录 `README.md`。

---

## 安装与环境

```bash
uv venv --python 3.12
source .venv/bin/activate
uv sync --extra dev
uv pip install -e .
uv pip install ".[test]"
```

---

## 数据库（Postgres/pgvector）

```bash
docker run --name qflow_agent_db \
  -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=qflow \
  -p 5432:5432 -d pgvector/pgvector:pg17

# 默认连接：postgresql://postgres:postgres@localhost:5432/qflow?sslmode=disable
```

仅 Postgres（无向量）
```bash
docker run --name qflow_agent_db \
  -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=qflow \
  -d -p 5432:5432 postgres
```

---

## 模型与 Key 配置

将 API 与 Base URL 写入 `src/common/models/.env`（字段参考 `src/common/models/model_list.py` 中的 assert）：

```
DEEPSEEK_BASE_URL=...
DEEPSEEK_API_KEY=...
PPIO_BASE_URL=...
PPIO_API_KEY=...
ALI_BASE_URL=...
ALI_API_KEY=...        # 亦用于 DashScope Embedding
SHANGTANG_BASE_URL=...
SHANGTANG_API_KEY=...
OPENROUTER_BASE_URL=...
OPENROUTER_API_KEY=...
GLM_BASE_URL=...
GLM_API_KEY=...
KIMI_BASE_URL=...
KIMI_API_KEY=...
```

自定义模型选择策略：编辑 `src/common/llm_models_config.py`（JSON/TEXT 双模式、LOW/MEDIUM/HIGH 分层、同级多模型权重）。

---

## 可视化调试（可选）

```bash
# 已在 pyproject.toml 的 dev 可选依赖中声明，直接同步安装：
uv sync --extra dev
uv run langgraph dev --allow-blocking
# 端口占用（示例）： lsof -ti:2024 | xargs kill -9
```

---

## 运行示例

```bash
# 🚀 主入口（统一启动器）
uv run apps/main.py

# 🌐 运行模式
uv run apps/main.py                      # 交互式：含恢复/删除/回退
# 数据库为空时：自动创建新项目

# 🔍 语义检索（按书ID查询设定/场景向量）
uv run apps/vector_query.py --book-id "your-book-id"
uv run apps/vector_query.py --book-id "your-book-id" --query "主角的身份"

 
```

### 从最新 checkpoint 恢复（推荐）

```bash
uv run apps/main.py --checkpoint-id latest
# 可选：也可指定书ID（若指定，则直接从该书最新 checkpoint 恢复）；不指定则全局最新
# uv run apps/main.py --resume-book-id YOUR_BOOK_ID --checkpoint-id latest
```

---

## 事件流显示与节流说明

说明：入口固定使用 `.astream(..., stream_mode=["updates", "debug"])` 输出事件；
`updates` 展示节点更新与中断；`debug` 事件经 `format_langgraph_debug_line` 统一成单行输出。

可用配置（`RunnableConfig` 顶层）：
- `recursion_limit`：限制 LangGraph 最大迭代步数。

可用配置（`config.configurable`）：
- `max_preview_len`：更新/消息预览最大长度（默认 400）
- `stream_preview_len`：模型 token 流节流长度（默认 120）
- `llm_text_min_length`：文本最小长度校验（整数，默认 0 表示关闭）
- `llm_text_sensitive_patterns`：敏感模式正则（字符串或数组），命中触发一次回退重试
- `llm_text_retry_tier`：回退重试时升级的模型等级（`low|medium|high`）
- `stream_post_chunk_size`：流式后处理分块大小（默认 2000）
- `stream_write_max_self_loops`：流写自循环上限，达到后强制进入分章（默认 8）

并发处理结束时，CLI 会从最近一次 `resume_info` 中解析并打印进度或缓冲信息：
- 任务进度：`[completed/total]`
- 流式缓冲与分块：`[buffer=..., chunks=...]`

存储稳健性（说明）：
- v2 数据层写入采用固定键（幂等覆盖），如章节 `chapter_{N}_{title}_full`、流式分块 `batch_{batch_id}_chunk_{i:03d}`。
- 关键写入启用轻量重试与退避：内部 `store_aput(..., retries=2, retry_backoff_s=0.5)`，无需用户配置。

 
---

## 审阅记录说明

审阅记录写入工作区目录的 `审阅记录.md` 等文件，包含节点/域/文件/卷信息/等级/操作等元数据。

---

## 删除项目（带 dry-run 确认）

通过 Checkpoint 启动器进行交互式删除；建议先 dry-run 预览清单：

```bash
uv run apps/main.py
# 选择 D 进入删除 → 选择项目 → 先 dry-run 预览（y）→ 确认实际删除（y）
```

或在代码中：

```python
from pathlib import Path
from src.langgraph.checkpoint import CheckpointManager

async with CheckpointManager.connect() as mgr:
    await mgr.delete_book(book_id="...", workspace_path=Path("./workspace"), dry_run=True)   # 预览
    await mgr.delete_book(book_id="...", workspace_path=Path("./workspace"), dry_run=False)  # 删除
```

## CLI 事件与 Streaming 显示

- 事件流固定使用 `.astream(..., stream_mode=["updates"])` 输出 updates/debug；入口：`apps/cli_events.py::stream_graph_events()` 调用 `EventHandler.run()`。
- 事件映射：
  - on_start/on_end：流程开始与结束
  - on_node_start/on_node_end：节点生命周期（名称与更新字段）
  - on_task_start/on_task_end：并发任务生命周期（预留）
  - on_chat_model_stream/on_llm_stream：模型 token 流（节流打印）
 - 预览长度：`config.configurable.max_preview_len` 控制打印的最大预览长度（默认 400）。
- Token 流节流：`config.configurable.stream_preview_len` 独立控制 `on_chat_model_stream` 的节流长度（默认 120）。

---

## 运行配置（RunnableConfig.configurable）

常用键：
- `thread_id`: 线程/会话标识
- `book_id`: 项目ID；由 `bs_planner` 在初始化时取 `configurable.thread_id` 写入 `state.book_id`，用于本地目录与存储命名空间（v2 命名空间：`writer_app_v2`）
- `workspace_path`: 本地工作区保存根目录
- `default_new_project`: 是否默认新项目（默认 True）
- `prompt_budget_tokens`: 上下文 Token 预算（启用裁剪）
- `demo_chapter`/`demo_bs`: 示例参考文本
- `enable_smart_character_management`: 是否启用智能角色需求分析
- `resume_book_id`/`checkpoint_id`: 进度恢复相关（`checkpoint_id` 支持特殊值 `latest`，独立于 `resume_book_id`，表示从全局最新 checkpoint 恢复）
- `max_preview_len`: 更新/消息预览最大长度（默认 400）
- `stream_preview_len`: 模型 token 流节流长度（默认 120）
- `skip_policies`: 审核跳过策略（详见“中断与 /skip”）

说明：
- 主入口 `apps/main.py` 集成了 `src/checkpoint.CheckpointManager` 的交互式恢复/删除能力：
  - `book_id` 默认等于项目首次创建时的 `thread_id`，后续同一本书可出现多个 `thread_id`（分支）。
  - 列出所有书籍（从 checkpointer 聚合），显示书名与最近时间；仅在恢复时才选择具体 `thread_id`。
  - 可选“回退”到某个历史 `checkpoint_id`（非重放，从该状态直接继续）。
  - 历史展示采用“树形结构”默认显示（包含每个 checkpoint 的完整关键信息），也支持按需改为列表。
  - 历史展示信息完全基于 `state['resume_info']` 中的最新摘要（由各函数式 `node` 在执行后写入）。统一使用 `NodeKit.build_resume_info()/build_stream_resume_info()` 生成公共字段，并追加业务摘要（步骤、卷、场景、缓冲等）。
    - 旧版 `checkpoint_hint` 与基于值的推断已移除。
  - 选择 0 新建（自动生成新的 `thread_id` 并从 planner 开始）。

---

## 模型配置（命令式）

```python
from src.common.llm_config import configure_json_models, configure_text_models
configure_json_models(low=["qwen3-30b-a3b-instruct-2507"], high=["deepseek-chat"]) 
```

---

## 故障排查
- 端口占用（2024）：`lsof -ti:2024 | xargs kill -9`
- 向量检索报错：检查 `ALI_API_KEY` 是否配置正确（DashScope Embedding）。
- 模型加载失败：检查 `src/common/models/.env` 是否补齐 BaseURL 与 API Key。
- 运行 `apps/langdev_client.py` 提示缺少 `langgraph_sdk`：执行 `uv pip install langgraph-sdk`。

---

## 中断与 /skip（审核策略）


---

## 批量文件命名规范

- 大纲细化批量：`outline/批量-第A~B卷-细化大纲.md`
- 场景设计批量：`scene/批量-第A~B卷-场景设计.md`
- 单卷文件：
  - 大纲细化单卷：`outline/第N卷-细化大纲-vM.md`
  - 场景清单单卷：`scene/第N卷场景清单.md`
  - 写作缓冲：`chapters_stream/stream_buffer.md`
  - 章节成品：`chapter/第N章-标题.md`
