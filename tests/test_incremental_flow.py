import pytest


@pytest.mark.asyncio
async def test_scene_append_mode_payload_building():
    from src.langgraph.nodes.common.book_types import BookDetail, BookSceneDesign, VolumeOutline, VolumeSceneDesign

    # 直接调用内部逻辑意义不大，这里做最小行为校验：
    # - 当 existing_count < 2 时，writer_scene 会给 scene_design_task payload 注入 append_mode 相关参数
    from src.langgraph.nodes.scene.scene_functional import writer_scene_fn

    # 构造最小状态：当前卷=1，只有一卷大纲；scene_design 有 1 个已存在场景
    vo = VolumeOutline(volume_number=1, volume_title="V1", volume_outline_content="...")
    bd = BookDetail(volume_outlines=[vo])
    vsd = VolumeSceneDesign(volume_number=1, scenes=[], design_completed=False)
    sd = BookSceneDesign(volume_scenes=[vsd])

    state = {
        "writer_current_plan": type(
            "P", (), {"is_all_step_completed": lambda s: True, "is_all_step_refined": lambda s: True}
        )(),
        "book_id": "B001",
        "book_name": "Book",
        "character_summaries": type("CS", (), {"characters": []})(),
        "character_details": type("CD", (), {"characters": []})(),
        "book_detail": bd,
        "scene_design": sd,
    }

    # 配置：关闭模型真实调用；这里只验证函数可运行并返回 Command
    cmd = await writer_scene_fn(state, {"configurable": {}})
    from langgraph.types import Command as LGCommand

    assert isinstance(cmd, LGCommand)
    assert hasattr(cmd, "goto")


@pytest.mark.asyncio
async def test_outline_next_volume_increment():
    from src.langgraph.nodes.common.book_types import BookDetail, VolumeOutline
    from src.langgraph.nodes.outlines.outline_functional import writer_outline_fn

    # 初始只有一卷
    vo = VolumeOutline(volume_number=1, volume_title="V1", volume_outline_content="...")
    bd = BookDetail(volume_outlines=[vo])

    state = {
        "writer_current_plan": type(
            "P", (), {"is_all_step_completed": lambda s: True, "is_all_step_refined": lambda s: True}
        )(),
        "book_id": "B001",
        "book_name": "Book",
        "character_summaries": type("CS", (), {"characters": []})(),
        "character_details": type("CD", (), {"characters": []})(),
        "book_detail": bd,
        "outline_generate_next_volume_number": 2,
    }

    # 由于会真实请求 LLM，这里仅校验能够进入代码路径到达 early return
    try:
        await writer_outline_fn(state, {"configurable": {}})
    except Exception:
        # 允许抛异常（因为没有模型支撑），测试只验证路径覆盖
        pass


def test_book_summary_block_indexing():
    from src.langgraph.nodes.common.book_types import BookSumary

    assert BookSumary.block10_index(10) == 1
    assert BookSumary.block10_index(11) == 2
    assert BookSumary.block50_index(50) == 1
    assert BookSumary.block50_index(51) == 2
