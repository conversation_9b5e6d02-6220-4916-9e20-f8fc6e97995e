import asyncio
import json
import pytest

from langgraph.checkpoint.memory import MemorySaver

from src.langgraph.entrypoint import build_qflow_workflow


@pytest.mark.asyncio
async def test_end_to_end_mock_basic():
    cp = MemorySaver()
    ep = build_qflow_workflow(cp)
    cfg = {"configurable": {"thread_id": "t1", "model_tier": "mock", "recursion_limit": 12}}
    inputs = {"messages": [], "configurable": {"thread_id": "t1", "model_tier": "mock"}}

    res = await ep.ainvoke(inputs, cfg)
    assert isinstance(res, dict)
    assert "next" in res and "steps" in res


@pytest.mark.asyncio
async def test_recovery_resume_after_partial_run():
    cp = MemorySaver()
    ep = build_qflow_workflow(cp)
    cfg = {"configurable": {"thread_id": "t2", "model_tier": "mock", "recursion_limit": 5}}
    inputs = {"messages": [], "configurable": {"thread_id": "t2", "model_tier": "mock"}}

    # 先跑一次，拿到保存点
    try:
        async for ev in ep.astream(inputs, cfg, stream_mode=["debug", "updates"]):
            pass
    except Exception:
        # mock 模式不应抛错
        assert False

    # 再次运行，应该不报错，并能继续推进
    res = await ep.ainvoke(inputs, cfg)
    assert isinstance(res, dict)


@pytest.mark.asyncio
async def test_step_decision_sequence_progresses():
    cp = MemorySaver()
    ep = build_qflow_workflow(cp)
    cfg = {"configurable": {"thread_id": "t3", "model_tier": "mock", "recursion_limit": 20}}
    inputs = {"messages": [], "configurable": {"thread_id": "t3", "model_tier": "mock"}}

    # 运行并收集 debug 事件，确保出现预期阶段
    seen = set()
    async for ev in ep.astream(inputs, cfg, stream_mode=["debug", "updates"]):
        if isinstance(ev, tuple) and len(ev) == 2 and isinstance(ev[1], dict):
            payload = ev[1].get("payload") or {}
            name = payload.get("name")
            if name in {"bs_planner_fn", "bs_steps_fn", "bs_refine_fn", "writer_character_fn"}:
                seen.add(name)
    assert {"bs_planner_fn", "bs_steps_fn", "bs_refine_fn"}.issubset(seen)

