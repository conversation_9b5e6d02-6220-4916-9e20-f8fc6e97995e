import pytest

from langgraph.checkpoint.memory import MemorySaver
from src.langgraph.entrypoint import build_qflow_workflow


@pytest.mark.asyncio
async def test_step_order_without_state_goes_planner():
    cp = MemorySaver()
    ep = build_qflow_workflow(cp)
    cfg = {"configurable": {"thread_id": "t4", "model_tier": "mock", "recursion_limit": 5}}
    inputs = {"messages": [], "configurable": {"thread_id": "t4", "model_tier": "mock"}}
    res = await ep.ainvoke(inputs, cfg)
    assert isinstance(res, dict)
