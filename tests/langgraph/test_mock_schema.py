import pytest

from src.langgraph.nodes.common.book_types import (
    BookVolumeOutlinesSchema,
    CharacterDetail,
    CharacterSummaryCollection,
    SegmentedChapterResult,
    VolumeOutline,
    WriterPlanSchema,
)
from src.langgraph.nodes.common.mock import make_mock_for_schema, mock_text


@pytest.mark.parametrize(
    "schema_type",
    [WriterPlanSchema, BookVolumeOutlinesSchema, CharacterSummaryCollection, CharacterDetail, SegmentedChapterResult],
)
def test_make_mock_for_schema_returns_instance(schema_type):
    obj = make_mock_for_schema(schema_type)
    # 允许 None/{} 但期望大多数 schema 能实例化
    assert obj is not None


def test_mock_text_returns_tuple():
    txt, cot = mock_text("node")
    assert isinstance(txt, str)
    assert cot == ""


@pytest.mark.parametrize("n", list(range(20)))
def test_make_mock_for_schema_many_calls(n):
    # 重复调用应稳定
    obj = make_mock_for_schema(WriterPlanSchema)
    assert getattr(obj, "plan_steps", None) is not None

