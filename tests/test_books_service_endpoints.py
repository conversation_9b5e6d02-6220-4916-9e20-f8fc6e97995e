import pytest

from src.fastapi.services.books_service import BooksService


@pytest.mark.asyncio
async def test_get_setting_returns_refined_fields(monkeypatch):
    svc = BooksService()

    async def fake_resolve(mgr, book_id, checkpoint_id, thread_id=None):
        return ("tid-1", "cid-1", "2025-01-01T00:00:00Z")

    async def fake_load(mgr, tid, cid):
        return {
            "refined_book_setting": "设定内容-ABC",
            "refined_book_outlines": "大纲内容-DEF",
        }

    monkeypatch.setattr("src.fastapi.services.books_service._resolve_thread_and_cp", fake_resolve)
    monkeypatch.setattr("src.fastapi.services.books_service._load_state", fake_load)

    data = await svc.get_setting("book-1", thread_id="tid-1", checkpoint_id="cid-1")
    assert data["refined_book_setting"] == "设定内容-ABC"
    assert data["refined_book_outlines"] == "大纲内容-DEF"
    assert data["meta"]["thread_id"] == "tid-1"
    assert data["meta"]["checkpoint_id"] == "cid-1"


@pytest.mark.asyncio
async def test_get_full_state_projection_contains_setting(monkeypatch):
    svc = BooksService()

    async def fake_resolve(mgr, book_id, checkpoint_id, thread_id=None):
        return ("tid-2", "cid-2", "2025-01-02T00:00:00Z")

    fake_state = {
        "writer_current_plan": {"plan_thought": "思路X", "plan_steps": []},
        "refined_book_setting": "设定内容-XYZ",
        "refined_book_outlines": "大纲内容-UVW",
        "book_detail": {"volume_outlines": [], "chapters": []},
        "steps": 3,
    }

    async def fake_load(mgr, tid, cid):
        return fake_state

    monkeypatch.setattr("src.fastapi.services.books_service._resolve_thread_and_cp", fake_resolve)
    monkeypatch.setattr("src.fastapi.services.books_service._load_state", fake_load)

    data = await svc.get_full_state("book-2", checkpoint_id="cid-2", thread_id="tid-2")
    proj = data["projection"]
    assert proj["setting"]["refined_book_setting"] == "设定内容-XYZ"
    assert proj["setting"]["refined_book_outlines"] == "大纲内容-UVW"
    # progress 已删除，不再包含 steps/buffer_len 等字段
